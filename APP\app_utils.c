// 文件名：app_utils.c
// 功能：应用层公共工具实现，提供通用的工具函数
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#include "app_utils.h"
#include "string.h"
#include "stdio.h"
#include "stdarg.h"
#include "main.h"
#include "config_manager.h"

// ========== 内部状态 ==========
static uint8_t g_utils_initialized = 0;

// ========== 内部函数声明 ==========
static app_config_status_t app_utils_init_internal(void);

// ========== 公共工具函数实现 ==========
app_config_status_t app_utils_init(void) // 初始化工具模块
{
    if (g_utils_initialized) return APP_CONFIG_OK;
    app_config_status_t status = app_utils_init_internal();
    if (status == APP_CONFIG_OK) g_utils_initialized = 1;
    return status;
}

static app_config_status_t app_utils_init_internal(void) // 内部初始化函数
{
    // 工具模块无需特殊初始化
    return APP_CONFIG_OK;
}

uint8_t app_utils_is_initialized(void) // 检查是否已初始化
{
    return g_utils_initialized;
}

// ========== 参数验证工具 ==========
uint8_t app_utils_validate_pointer(const void* ptr) // 验证指针有效性
{
    return ptr != NULL;
}

uint8_t app_utils_validate_range_uint32(uint32_t val, uint32_t min, uint32_t max) // 验证uint32范围
{
    return (val >= min) && (val <= max);
}

uint8_t app_utils_validate_range_float(float val, float min, float max) // 验证float范围
{
    return (val >= min) && (val <= max);
}

uint8_t app_utils_validate_buffer_size(uint32_t size) // 验证缓冲区大小
{
    return APP_CONFIG_VALIDATE_BUFFER_SIZE(size);
}

// ========== 字符串处理工具 ==========
app_config_status_t app_utils_string_init(app_string_buffer_t* str_buf, char* buffer, uint32_t size) // 初始化字符串缓冲区
{
    APP_VALIDATE_POINTER(str_buf);
    APP_VALIDATE_POINTER(buffer);
    APP_VALIDATE_BUFFER_SIZE_RETURN(size, APP_CONFIG_INVALID_PARAM);
    
    str_buf->buffer = buffer;
    str_buf->size = size;
    str_buf->length = 0;
    str_buf->buffer[0] = '\0';
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_string_append(app_string_buffer_t* str_buf, const char* text) // 追加字符串
{
    APP_VALIDATE_POINTER(str_buf);
    APP_VALIDATE_POINTER(text);
    
    uint32_t text_len = strlen(text);
    if (str_buf->length + text_len >= str_buf->size) {
        return APP_CONFIG_OUT_OF_RANGE;
    }
    
    strcat(str_buf->buffer, text);
    str_buf->length += text_len;
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_string_format(app_string_buffer_t* str_buf, const char* format, ...) // 格式化字符串
{
    APP_VALIDATE_POINTER(str_buf);
    APP_VALIDATE_POINTER(format);
    
    va_list args;
    va_start(args, format);
    
    int result = vsnprintf(str_buf->buffer + str_buf->length, 
                          str_buf->size - str_buf->length, 
                          format, args);
    
    va_end(args);
    
    if (result < 0 || (uint32_t)result >= (str_buf->size - str_buf->length)) {
        return APP_CONFIG_OUT_OF_RANGE;
    }
    
    str_buf->length += result;
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_string_clear(app_string_buffer_t* str_buf) // 清空字符串
{
    APP_VALIDATE_POINTER(str_buf);
    
    str_buf->length = 0;
    str_buf->buffer[0] = '\0';
    
    return APP_CONFIG_OK;
}

uint32_t app_utils_string_length(const app_string_buffer_t* str_buf) // 获取字符串长度
{
    return str_buf ? str_buf->length : 0;
}

// ========== 时间戳工具 ==========
app_config_status_t app_utils_timer_init(app_timer_t* timer, uint32_t duration) // 初始化定时器
{
    APP_VALIDATE_POINTER(timer);
    
    timer->timestamp = 0;
    timer->duration = duration;
    timer->active = 0;
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_timer_start(app_timer_t* timer) // 启动定时器
{
    APP_VALIDATE_POINTER(timer);
    
    timer->timestamp = HAL_GetTick();
    timer->active = 1;
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_timer_stop(app_timer_t* timer) // 停止定时器
{
    APP_VALIDATE_POINTER(timer);
    
    timer->active = 0;
    
    return APP_CONFIG_OK;
}

uint8_t app_utils_timer_is_expired(const app_timer_t* timer) // 检查定时器是否过期
{
    if (!timer || !timer->active) return 0;
    
    uint32_t current_time = HAL_GetTick();
    return (current_time - timer->timestamp) >= timer->duration;
}

uint32_t app_utils_timer_get_elapsed(const app_timer_t* timer) // 获取已过时间
{
    if (!timer || !timer->active) return 0;
    
    uint32_t current_time = HAL_GetTick();
    return current_time - timer->timestamp;
}

// ========== 状态机工具 ==========
app_config_status_t app_utils_state_machine_init(app_state_machine_t* sm) // 初始化状态机
{
    APP_VALIDATE_POINTER(sm);
    
    sm->current_state = APP_STATE_MACHINE_IDLE;
    sm->previous_state = APP_STATE_MACHINE_IDLE;
    sm->state_enter_time = HAL_GetTick();
    sm->state_duration = 0;
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_state_machine_set_state(app_state_machine_t* sm, app_state_machine_state_t new_state) // 设置状态
{
    APP_VALIDATE_POINTER(sm);
    
    if (sm->current_state != new_state) {
        sm->previous_state = sm->current_state;
        sm->current_state = new_state;
        sm->state_enter_time = HAL_GetTick();
    }
    
    return APP_CONFIG_OK;
}

app_state_machine_state_t app_utils_state_machine_get_state(const app_state_machine_t* sm) // 获取当前状态
{
    return sm ? sm->current_state : APP_STATE_MACHINE_ERROR;
}

uint32_t app_utils_state_machine_get_duration(const app_state_machine_t* sm) // 获取状态持续时间
{
    if (!sm) return 0;
    
    uint32_t current_time = HAL_GetTick();
    return current_time - sm->state_enter_time;
}

// ========== 数组处理工具 ==========
app_config_status_t app_utils_array_init_uint8(uint8_t* array, uint32_t count, uint8_t init_value) // 初始化uint8数组
{
    APP_VALIDATE_POINTER(array);
    
    for (uint32_t i = 0; i < count; i++) {
        array[i] = init_value;
    }
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_array_init_uint32(uint32_t* array, uint32_t count, uint32_t init_value) // 初始化uint32数组
{
    APP_VALIDATE_POINTER(array);
    
    for (uint32_t i = 0; i < count; i++) {
        array[i] = init_value;
    }
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_array_init_float(float* array, uint32_t count, float init_value) // 初始化float数组
{
    APP_VALIDATE_POINTER(array);
    
    for (uint32_t i = 0; i < count; i++) {
        array[i] = init_value;
    }
    
    return APP_CONFIG_OK;
}

uint8_t app_utils_array_validate_uint8(const uint8_t* array, uint32_t count, uint8_t min, uint8_t max) // 验证uint8数组
{
    if (!array) return 0;
    
    for (uint32_t i = 0; i < count; i++) {
        if (array[i] < min || array[i] > max) {
            return 0;
        }
    }
    
    return 1;
}

uint8_t app_utils_array_validate_float(const float* array, uint32_t count, float min, float max) // 验证float数组
{
    if (!array) return 0;
    
    for (uint32_t i = 0; i < count; i++) {
        if (array[i] < min || array[i] > max) {
            return 0;
        }
    }
    
    return 1;
}

// ========== 配置获取工具 ==========
app_config_status_t app_utils_get_limit_value(float* limit_value) // 获取限值配置
{
    APP_VALIDATE_POINTER(limit_value);
    
    config_params_t config_params;
    if (config_get_params(&config_params) == CONFIG_OK) {
        *limit_value = config_params.limit;
    } else {
        *limit_value = app_config_get_limit_default();
    }
    
    return APP_CONFIG_OK;
}

app_config_status_t app_utils_get_ratio_value(float* ratio_value) // 获取比率配置
{
    APP_VALIDATE_POINTER(ratio_value);
    
    config_params_t config_params;
    if (config_get_params(&config_params) == CONFIG_OK) {
        *ratio_value = config_params.ratio;
    } else {
        *ratio_value = app_config_get_ratio_default();
    }
    
    return APP_CONFIG_OK;
}

// ========== 错误处理工具 ==========
const char* app_utils_get_error_string(app_config_status_t status) // 获取错误描述字符串
{
    return app_config_get_status_string(status);
}

// ========== 数学工具 ==========
float app_utils_clamp_float(float value, float min, float max) // 限制float值范围
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

uint32_t app_utils_clamp_uint32(uint32_t value, uint32_t min, uint32_t max) // 限制uint32值范围
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

uint8_t app_utils_is_power_of_two(uint32_t value) // 检查是否为2的幂
{
    return value > 0 && (value & (value - 1)) == 0;
}
