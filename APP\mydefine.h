// 文件名：mydefine.h
// 功能：全局定义和配置管理头文件，提供统一的系统配置和依赖管理
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __MYDEFINE_H__
#define __MYDEFINE_H__

// ========== 标准库包含 ==========
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdint.h"
#include "stdlib.h"
#include "math.h"

// ========== HAL库包含 ==========
#include "main.h"
#include "usart.h"
#include "adc.h"
#include "tim.h"
#include "dac.h"
#include "i2c.h"

// ========== 第三方组件包含 ==========
#include "WouoUI.h"
#include "WouoUI_user.h"
#include "u8g2.h"
#include "oled.h"
#include "lfs.h"
#include "lfs_port.h"
#include "gd25qxx.h"
#include "ringbuffer.h"
#include "arm_math.h"
#include "ff.h"    // FATFS文件系统
#include "fatfs.h" // FATFS配置

// ========== 应用层模块包含 ==========
#include "app_config.h"       // 统一配置管理
#include "app_state.h"        // 统一状态管理
#include "app_events.h"       // 统一事件管理
#include "app_utils.h"        // 公共工具模块(新增)
#include "scheduler.h"
#include "oled_app.h"
#include "adc_app.h"
#include "led_app.h"
#include "btn_app.h"
#include "flash_app.h"
#include "usart_app.h"
#include "rtc_app.h"
#include "system_check.h"
#include "config_manager.h"
#include "ini_parser.h"
#include "sampling_control.h"

extern uint16_t uart_rx_index;
extern uint32_t uart_rx_ticks;
extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_dma_buffer[128];
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];
extern uint8_t uart_send_flag;
extern uint8_t wave_analysis_flag;
extern u8g2_t u8g2; // ??? u8g2 ???
extern struct lfs_config cfg;
extern lfs_t lfs;
extern RTC_HandleTypeDef hrtc;

// FATFS?????????
extern uint8_t retSD;  // SD???????
extern char SDPath[4]; // SD?????????????
extern FATFS SDFatFS;  // SD???????????
extern FIL SDFile;     // SD?????????