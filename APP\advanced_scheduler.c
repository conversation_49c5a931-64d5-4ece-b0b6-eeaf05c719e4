// 文件名：advanced_scheduler.c
// 功能：高级任务调度器实现，提供灵活的任务管理和调度机制
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#include "advanced_scheduler.h"
#include "app_utils.h"
#include "hardware_abstraction.h"
#include "string.h"
#include "stdio.h"
#include "mydefine.h"

// ========== 内部状态管理 ==========
static scheduler_control_t g_scheduler = {0};

// ========== 内部函数声明 ==========
static uint8_t find_free_task_slot(void);
static scheduler_task_t* get_task_by_id(uint8_t task_id);
static uint8_t is_task_ready_to_run(const scheduler_task_t* task, uint32_t current_time);
static uint8_t get_highest_priority_ready_task(uint32_t current_time);
static void execute_task(scheduler_task_t* task);
static void update_task_stats(scheduler_task_t* task, uint32_t execution_time);
static void handle_task_error(scheduler_task_t* task, const char* error_msg);

// ========== 调度器管理接口实现 ==========
scheduler_status_t advanced_scheduler_init(void) // 初始化调度器
{
    if (g_scheduler.initialized) return SCHEDULER_STATUS_OK;

    // 清零调度器控制结构
    memset(&g_scheduler, 0, sizeof(scheduler_control_t));

    // 初始化基本参数
    g_scheduler.scheduler_tick = hal_timer_get_tick();
    g_scheduler.initialized = 1;

    return SCHEDULER_STATUS_OK;
}

scheduler_status_t advanced_scheduler_deinit(void) // 反初始化调度器
{
    if (!g_scheduler.initialized) return SCHEDULER_STATUS_NOT_INITIALIZED;

    advanced_scheduler_stop();
    memset(&g_scheduler, 0, sizeof(scheduler_control_t));

    return SCHEDULER_STATUS_OK;
}

scheduler_status_t advanced_scheduler_start(void) // 启动调度器
{
    if (!g_scheduler.initialized) return SCHEDULER_STATUS_NOT_INITIALIZED;

    g_scheduler.running = 1;
    g_scheduler.scheduler_tick = hal_timer_get_tick();

    return SCHEDULER_STATUS_OK;
}

scheduler_status_t advanced_scheduler_stop(void) // 停止调度器
{
    if (!g_scheduler.initialized) return SCHEDULER_STATUS_NOT_INITIALIZED;

    g_scheduler.running = 0;
    g_scheduler.current_task_id = SCHEDULER_INVALID_TASK_ID;

    return SCHEDULER_STATUS_OK;
}

scheduler_status_t advanced_scheduler_run(void) // 运行调度器(主循环调用)
{
    if (!g_scheduler.initialized || !g_scheduler.running) {
        return SCHEDULER_STATUS_NOT_INITIALIZED;
    }

    uint32_t current_time = hal_timer_get_tick();
    g_scheduler.scheduler_tick = current_time;

    // 查找最高优先级的就绪任务
    uint8_t task_id = get_highest_priority_ready_task(current_time);
    if (task_id == SCHEDULER_INVALID_TASK_ID) {
        return SCHEDULER_STATUS_OK; // 没有就绪任务
    }

    scheduler_task_t* task = get_task_by_id(task_id);
    if (task && task->enabled && task->state == TASK_STATE_READY) {
        execute_task(task);
    }

    return SCHEDULER_STATUS_OK;
}

uint8_t scheduler_is_running(void) // 检查调度器是否运行
{
    return g_scheduler.initialized && g_scheduler.running;
}

// ========== 任务管理接口实现 ==========
uint8_t scheduler_add_task(const char* task_name,                      // 添加任务
                          scheduler_task_func_t task_func,
                          scheduler_task_priority_t priority,
                          scheduler_task_type_t type,
                          uint32_t period_ms)
{
    if (!g_scheduler.initialized) return SCHEDULER_INVALID_TASK_ID;
    if (!task_name || !task_func) return SCHEDULER_INVALID_TASK_ID;
    if (g_scheduler.task_count >= SCHEDULER_MAX_TASKS) return SCHEDULER_INVALID_TASK_ID;
    
    uint8_t slot = find_free_task_slot();
    if (slot == SCHEDULER_INVALID_TASK_ID) return SCHEDULER_INVALID_TASK_ID;
    
    scheduler_task_t* task = &g_scheduler.tasks[slot];
    
    // 初始化任务控制块
    task->task_id = slot;
    app_utils_string_init((app_string_buffer_t*)task->task_name, task->task_name, SCHEDULER_TASK_NAME_LEN);
    strncpy(task->task_name, task_name, SCHEDULER_TASK_NAME_LEN - 1);
    task->task_name[SCHEDULER_TASK_NAME_LEN - 1] = '\0';
    
    task->task_func = task_func;
    task->error_handler = NULL;
    task->state = TASK_STATE_READY;
    task->priority = priority;
    task->type = type;
    task->period_ms = period_ms;
    task->last_run_time = 0;
    task->next_run_time = hal_timer_get_tick() + period_ms;
    task->timeout_ms = period_ms * 2; // 默认超时时间为周期的2倍
    task->enabled = 1;
    
    // 清零统计信息
    memset(&task->stats, 0, sizeof(scheduler_task_stats_t));
    
    g_scheduler.task_count++;
    
    return slot;
}

scheduler_status_t scheduler_remove_task(uint8_t task_id) // 删除任务
{
    if (!g_scheduler.initialized) return SCHEDULER_STATUS_NOT_INITIALIZED;
    
    scheduler_task_t* task = get_task_by_id(task_id);
    if (!task) return SCHEDULER_STATUS_NOT_FOUND;
    
    // 清零任务控制块
    memset(task, 0, sizeof(scheduler_task_t));
    task->task_id = SCHEDULER_INVALID_TASK_ID;
    task->state = TASK_STATE_INACTIVE;
    
    g_scheduler.task_count--;
    
    return SCHEDULER_STATUS_OK;
}

scheduler_status_t scheduler_enable_task(uint8_t task_id) // 启用任务
{
    if (!g_scheduler.initialized) return SCHEDULER_STATUS_NOT_INITIALIZED;
    
    scheduler_task_t* task = get_task_by_id(task_id);
    if (!task) return SCHEDULER_STATUS_NOT_FOUND;
    
    task->enabled = 1;
    if (task->state == TASK_STATE_INACTIVE) {
        task->state = TASK_STATE_READY;
    }
    
    return SCHEDULER_STATUS_OK;
}

scheduler_status_t scheduler_disable_task(uint8_t task_id) // 禁用任务
{
    if (!g_scheduler.initialized) return SCHEDULER_STATUS_NOT_INITIALIZED;
    
    scheduler_task_t* task = get_task_by_id(task_id);
    if (!task) return SCHEDULER_STATUS_NOT_FOUND;
    
    task->enabled = 0;
    task->state = TASK_STATE_INACTIVE;
    
    return SCHEDULER_STATUS_OK;
}

// ========== 内部函数实现 ==========
static uint8_t find_free_task_slot(void) // 查找空闲任务槽
{
    for (uint8_t i = 0; i < SCHEDULER_MAX_TASKS; i++) {
        if (g_scheduler.tasks[i].task_id == SCHEDULER_INVALID_TASK_ID || 
            g_scheduler.tasks[i].state == TASK_STATE_INACTIVE) {
            return i;
        }
    }
    return SCHEDULER_INVALID_TASK_ID;
}

static scheduler_task_t* get_task_by_id(uint8_t task_id) // 根据ID获取任务
{
    if (task_id >= SCHEDULER_MAX_TASKS) return NULL;
    
    scheduler_task_t* task = &g_scheduler.tasks[task_id];
    if (task->task_id == task_id && task->state != TASK_STATE_INACTIVE) {
        return task;
    }
    
    return NULL;
}

static uint8_t is_task_ready_to_run(const scheduler_task_t* task, uint32_t current_time) // 检查任务是否就绪
{
    if (!task || !task->enabled || task->state != TASK_STATE_READY) {
        return 0;
    }
    
    switch (task->type) {
        case TASK_TYPE_PERIODIC:
            return (current_time >= task->next_run_time);
        case TASK_TYPE_ONE_SHOT:
            return (task->last_run_time == 0); // 只执行一次
        case TASK_TYPE_EVENT_DRIVEN:
            return 1; // 事件驱动任务总是就绪
        default:
            return 0;
    }
}

static uint8_t get_highest_priority_ready_task(uint32_t current_time) // 获取最高优先级就绪任务
{
    uint8_t best_task_id = SCHEDULER_INVALID_TASK_ID;
    scheduler_task_priority_t highest_priority = TASK_PRIORITY_IDLE;
    
    for (uint8_t i = 0; i < SCHEDULER_MAX_TASKS; i++) {
        scheduler_task_t* task = &g_scheduler.tasks[i];
        
        if (is_task_ready_to_run(task, current_time)) {
            if (task->priority > highest_priority) {
                highest_priority = task->priority;
                best_task_id = i;
            }
        }
    }
    
    return best_task_id;
}

static void execute_task(scheduler_task_t* task) // 执行任务
{
    if (!task || !task->task_func) return;
    
    uint32_t start_time = hal_timer_get_tick();
    task->state = TASK_STATE_RUNNING;
    g_scheduler.current_task_id = task->task_id;
    
    // 执行任务函数
    task->task_func();
    
    uint32_t end_time = hal_timer_get_tick();
    uint32_t execution_time = end_time - start_time;
    
    // 更新任务状态和统计信息
    task->last_run_time = start_time;
    task->state = TASK_STATE_READY;
    
    // 计算下次运行时间
    if (task->type == TASK_TYPE_PERIODIC) {
        task->next_run_time = start_time + task->period_ms;
    } else if (task->type == TASK_TYPE_ONE_SHOT) {
        task->enabled = 0; // 单次任务执行后自动禁用
        task->state = TASK_STATE_INACTIVE;
    }
    
    update_task_stats(task, execution_time);
    
    g_scheduler.current_task_id = SCHEDULER_INVALID_TASK_ID;
    g_scheduler.total_tasks_executed++;
    g_scheduler.total_execution_time += execution_time;
}

static void update_task_stats(scheduler_task_t* task, uint32_t execution_time) // 更新任务统计
{
    if (!task) return;
    
    task->stats.execution_count++;
    task->stats.total_execution_time += execution_time;
    task->stats.last_execution_time = execution_time;
    
    if (execution_time > task->stats.max_execution_time) {
        task->stats.max_execution_time = execution_time;
    }
}

// ========== 任务查询接口实现 ==========
uint8_t scheduler_find_task_by_name(const char* task_name) // 按名称查找任务
{
    if (!task_name || !g_scheduler.initialized) return SCHEDULER_INVALID_TASK_ID;

    for (uint8_t i = 0; i < SCHEDULER_MAX_TASKS; i++) {
        scheduler_task_t* task = &g_scheduler.tasks[i];
        if (task->state != TASK_STATE_INACTIVE &&
            strcmp(task->task_name, task_name) == 0) {
            return i;
        }
    }

    return SCHEDULER_INVALID_TASK_ID;
}

scheduler_task_state_t scheduler_get_task_state(uint8_t task_id) // 获取任务状态
{
    scheduler_task_t* task = get_task_by_id(task_id);
    return task ? task->state : TASK_STATE_INACTIVE;
}

uint8_t scheduler_get_task_count(void) // 获取任务数量
{
    return g_scheduler.task_count;
}

// ========== 预定义任务注册接口实现 ==========
scheduler_status_t scheduler_register_system_tasks(void) // 注册系统任务
{
    if (!g_scheduler.initialized) return SCHEDULER_STATUS_NOT_INITIALIZED;

    // 注册LED任务
    uint8_t led_task_id = scheduler_add_task("led_task", led_task,
                                            TASK_PRIORITY_LOW, TASK_TYPE_PERIODIC, 1);
    if (led_task_id == SCHEDULER_INVALID_TASK_ID) return SCHEDULER_STATUS_FULL;

    // 注册ADC任务
    uint8_t adc_task_id = scheduler_add_task("adc_task", adc_task,
                                            TASK_PRIORITY_NORMAL, TASK_TYPE_PERIODIC, 100);
    if (adc_task_id == SCHEDULER_INVALID_TASK_ID) return SCHEDULER_STATUS_FULL;

    // 注册按键任务
    uint8_t btn_task_id = scheduler_add_task("btn_task", btn_task,
                                            TASK_PRIORITY_HIGH, TASK_TYPE_PERIODIC, 5);
    if (btn_task_id == SCHEDULER_INVALID_TASK_ID) return SCHEDULER_STATUS_FULL;

    // 注册串口任务
    uint8_t uart_task_id = scheduler_add_task("uart_task", uart_task,
                                             TASK_PRIORITY_HIGH, TASK_TYPE_PERIODIC, 5);
    if (uart_task_id == SCHEDULER_INVALID_TASK_ID) return SCHEDULER_STATUS_FULL;

    // 注册OLED任务
    uint8_t oled_task_id = scheduler_add_task("oled_task", oled_task,
                                             TASK_PRIORITY_LOW, TASK_TYPE_PERIODIC, 1);
    if (oled_task_id == SCHEDULER_INVALID_TASK_ID) return SCHEDULER_STATUS_FULL;

    // 注册采样任务
    uint8_t sampling_task_id = scheduler_add_task("sampling_task", sampling_task,
                                                 TASK_PRIORITY_NORMAL, TASK_TYPE_PERIODIC, 10);
    if (sampling_task_id == SCHEDULER_INVALID_TASK_ID) return SCHEDULER_STATUS_FULL;

    return SCHEDULER_STATUS_OK;
}

// ========== 状态字符串接口实现 ==========
const char* scheduler_get_status_string(scheduler_status_t status) // 获取状态字符串
{
    switch (status) {
        case SCHEDULER_STATUS_OK:              return "Scheduler OK";
        case SCHEDULER_STATUS_ERROR:           return "Scheduler Error";
        case SCHEDULER_STATUS_FULL:            return "Task Queue Full";
        case SCHEDULER_STATUS_NOT_FOUND:       return "Task Not Found";
        case SCHEDULER_STATUS_INVALID_PARAM:   return "Invalid Parameter";
        case SCHEDULER_STATUS_NOT_INITIALIZED: return "Not Initialized";
        default:                               return "Unknown Status";
    }
}

const char* scheduler_get_task_state_string(scheduler_task_state_t state) // 获取任务状态字符串
{
    switch (state) {
        case TASK_STATE_INACTIVE:  return "Inactive";
        case TASK_STATE_READY:     return "Ready";
        case TASK_STATE_RUNNING:   return "Running";
        case TASK_STATE_SUSPENDED: return "Suspended";
        case TASK_STATE_ERROR:     return "Error";
        default:                   return "Unknown";
    }
}

const char* scheduler_get_task_priority_string(scheduler_task_priority_t priority) // 获取优先级字符串
{
    switch (priority) {
        case TASK_PRIORITY_IDLE:     return "Idle";
        case TASK_PRIORITY_LOW:      return "Low";
        case TASK_PRIORITY_NORMAL:   return "Normal";
        case TASK_PRIORITY_HIGH:     return "High";
        case TASK_PRIORITY_CRITICAL: return "Critical";
        default:                     return "Unknown";
    }
}
