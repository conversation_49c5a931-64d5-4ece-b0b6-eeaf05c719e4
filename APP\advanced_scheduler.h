// 文件名：advanced_scheduler.h
// 功能：高级任务调度器头文件，提供灵活的任务管理和调度机制
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __ADVANCED_SCHEDULER_H__
#define __ADVANCED_SCHEDULER_H__

#include "stdint.h"
#include "app_config.h"

// ========== 调度器配置常量 ==========
#define SCHEDULER_MAX_TASKS         16          // 最大任务数量
#define SCHEDULER_TASK_NAME_LEN     16          // 任务名称最大长度
#define SCHEDULER_INVALID_TASK_ID   0xFF        // 无效任务ID

// ========== 任务状态枚举 ==========
typedef enum {
    TASK_STATE_INACTIVE = 0,        // 任务未激活
    TASK_STATE_READY = 1,           // 任务就绪
    TASK_STATE_RUNNING = 2,         // 任务运行中
    TASK_STATE_SUSPENDED = 3,       // 任务挂起
    TASK_STATE_ERROR = 4            // 任务错误
} scheduler_task_state_t;

// ========== 任务优先级枚举 ==========
typedef enum {
    TASK_PRIORITY_IDLE = 0,         // 空闲优先级
    TASK_PRIORITY_LOW = 1,          // 低优先级
    TASK_PRIORITY_NORMAL = 2,       // 普通优先级
    TASK_PRIORITY_HIGH = 3,         // 高优先级
    TASK_PRIORITY_CRITICAL = 4      // 关键优先级
} scheduler_task_priority_t;

// ========== 任务类型枚举 ==========
typedef enum {
    TASK_TYPE_PERIODIC = 0,         // 周期性任务
    TASK_TYPE_ONE_SHOT = 1,         // 单次执行任务
    TASK_TYPE_EVENT_DRIVEN = 2      // 事件驱动任务
} scheduler_task_type_t;

// ========== 调度器状态枚举 ==========
typedef enum {
    SCHEDULER_STATUS_OK = 0,        // 调度器正常
    SCHEDULER_STATUS_ERROR = 1,     // 调度器错误
    SCHEDULER_STATUS_FULL = 2,      // 任务队列已满
    SCHEDULER_STATUS_NOT_FOUND = 3, // 任务未找到
    SCHEDULER_STATUS_INVALID_PARAM = 4, // 参数无效
    SCHEDULER_STATUS_NOT_INITIALIZED = 5 // 未初始化
} scheduler_status_t;

// ========== 任务函数类型定义 ==========
typedef void (*scheduler_task_func_t)(void);                           // 任务执行函数
typedef void (*scheduler_task_error_handler_t)(uint8_t task_id, const char* error_msg); // 任务错误处理函数

// ========== 任务统计信息结构 ==========
typedef struct {
    uint32_t execution_count;       // 执行次数
    uint32_t total_execution_time;  // 总执行时间(ms)
    uint32_t max_execution_time;    // 最大执行时间(ms)
    uint32_t last_execution_time;   // 上次执行时间(ms)
    uint32_t error_count;           // 错误次数
} scheduler_task_stats_t;

// ========== 任务控制块结构 ==========
typedef struct {
    uint8_t task_id;                            // 任务ID
    char task_name[SCHEDULER_TASK_NAME_LEN];    // 任务名称
    scheduler_task_func_t task_func;            // 任务执行函数
    scheduler_task_error_handler_t error_handler; // 错误处理函数
    scheduler_task_state_t state;               // 任务状态
    scheduler_task_priority_t priority;         // 任务优先级
    scheduler_task_type_t type;                 // 任务类型
    uint32_t period_ms;                         // 执行周期(ms)
    uint32_t last_run_time;                     // 上次运行时间
    uint32_t next_run_time;                     // 下次运行时间
    uint32_t timeout_ms;                        // 任务超时时间(ms)
    uint8_t enabled;                            // 任务使能标志
    scheduler_task_stats_t stats;               // 任务统计信息
} scheduler_task_t;

// ========== 调度器控制结构 ==========
typedef struct {
    scheduler_task_t tasks[SCHEDULER_MAX_TASKS]; // 任务数组
    uint8_t task_count;                         // 当前任务数量
    uint8_t current_task_id;                    // 当前执行任务ID
    uint32_t scheduler_tick;                    // 调度器时钟计数
    uint32_t total_tasks_executed;              // 总执行任务数
    uint32_t total_execution_time;              // 总执行时间
    uint8_t initialized;                        // 初始化标志
    uint8_t running;                            // 运行标志
} scheduler_control_t;

// ========== 调度器管理接口 ==========
scheduler_status_t advanced_scheduler_init(void);                      // 初始化调度器
scheduler_status_t advanced_scheduler_deinit(void);                    // 反初始化调度器
scheduler_status_t advanced_scheduler_start(void);                     // 启动调度器
scheduler_status_t advanced_scheduler_stop(void);                      // 停止调度器
scheduler_status_t advanced_scheduler_run(void);                       // 运行调度器(主循环调用)
uint8_t scheduler_is_running(void);                                    // 检查调度器是否运行

// ========== 任务管理接口 ==========
uint8_t scheduler_add_task(const char* task_name,                      // 添加任务
                          scheduler_task_func_t task_func,
                          scheduler_task_priority_t priority,
                          scheduler_task_type_t type,
                          uint32_t period_ms);

scheduler_status_t scheduler_remove_task(uint8_t task_id);             // 删除任务
scheduler_status_t scheduler_remove_task_by_name(const char* task_name); // 按名称删除任务
scheduler_status_t scheduler_enable_task(uint8_t task_id);             // 启用任务
scheduler_status_t scheduler_disable_task(uint8_t task_id);            // 禁用任务
scheduler_status_t scheduler_suspend_task(uint8_t task_id);            // 挂起任务
scheduler_status_t scheduler_resume_task(uint8_t task_id);             // 恢复任务

// ========== 任务配置接口 ==========
scheduler_status_t scheduler_set_task_period(uint8_t task_id, uint32_t period_ms); // 设置任务周期
scheduler_status_t scheduler_set_task_priority(uint8_t task_id, scheduler_task_priority_t priority); // 设置任务优先级
scheduler_status_t scheduler_set_task_timeout(uint8_t task_id, uint32_t timeout_ms); // 设置任务超时
scheduler_status_t scheduler_set_task_error_handler(uint8_t task_id, scheduler_task_error_handler_t handler); // 设置错误处理函数

// ========== 任务查询接口 ==========
uint8_t scheduler_find_task_by_name(const char* task_name);            // 按名称查找任务
scheduler_task_state_t scheduler_get_task_state(uint8_t task_id);      // 获取任务状态
scheduler_task_priority_t scheduler_get_task_priority(uint8_t task_id); // 获取任务优先级
uint32_t scheduler_get_task_period(uint8_t task_id);                   // 获取任务周期
const char* scheduler_get_task_name(uint8_t task_id);                  // 获取任务名称

// ========== 任务统计接口 ==========
scheduler_status_t scheduler_get_task_stats(uint8_t task_id, scheduler_task_stats_t* out_stats); // 获取任务统计
scheduler_status_t scheduler_reset_task_stats(uint8_t task_id);        // 重置任务统计
uint8_t scheduler_get_task_count(void);                                // 获取任务数量
uint32_t scheduler_get_total_execution_time(void);                     // 获取总执行时间

// ========== 调度器信息接口 ==========
const char* scheduler_get_status_string(scheduler_status_t status);    // 获取状态字符串
const char* scheduler_get_task_state_string(scheduler_task_state_t state); // 获取任务状态字符串
const char* scheduler_get_task_priority_string(scheduler_task_priority_t priority); // 获取优先级字符串
scheduler_status_t scheduler_print_task_list(void);                    // 打印任务列表
scheduler_status_t scheduler_print_statistics(void);                   // 打印统计信息

// ========== 高级调度功能 ==========
scheduler_status_t scheduler_trigger_task(uint8_t task_id);            // 手动触发任务执行
scheduler_status_t scheduler_delay_task(uint8_t task_id, uint32_t delay_ms); // 延迟任务执行
scheduler_status_t scheduler_set_task_deadline(uint8_t task_id, uint32_t deadline_ms); // 设置任务截止时间

// ========== 预定义任务注册接口 ==========
scheduler_status_t scheduler_register_system_tasks(void);              // 注册系统任务

#endif // __ADVANCED_SCHEDULER_H__
