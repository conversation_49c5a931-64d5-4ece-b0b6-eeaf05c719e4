
#include "data_storage.h"
#include "fatfs.h"     // FATFS文件系统
#include "rtc_app.h"   // RTC时间管理
#include "usart_app.h" // 串口通信
#include "string.h"    // 字符串处理
#include "stdio.h"     // 标准输入输出
#include "app_config.h" // 统一配置管理

extern uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date); // ???????????????????usart_app.c??
extern void format_hex_output(uint32_t timestamp, float voltage, uint8_t is_overlimit, char *output); // ???????????

static file_state_t g_file_states[STORAGE_TYPE_COUNT]; // ?????????
static uint32_t g_boot_count = 0;                      // ??????????

static const char *g_storage_directory_names[STORAGE_TYPE_COUNT] = { // 存储目录名称数组
    "sample",     // STORAGE_SAMPLE
    "over_limit", // STORAGE_OVERLIMIT - 使用下划线分隔
    "log",        // STORAGE_LOG
    "hide_data"   // STORAGE_HIDEDATA - 使用下划线分隔
};

static const char *g_storage_filename_prefixes[STORAGE_TYPE_COUNT] = { // 存储文件名前缀数组
    "sample_data", // STORAGE_SAMPLE - 使用下划线分隔
    "over_limit",  // STORAGE_OVERLIMIT - 使用下划线分隔
    "log",         // STORAGE_LOG
    "hide_data"    // STORAGE_HIDEDATA - 使用下划线分隔
};

static uint32_t get_boot_count_from_fatfs(void) // ??FATFS??????????? ????:?? ????:?????????
{
    FIL file;                // ??????
    uint32_t boot_count = 0; // ????????
    UINT bytes_read;         // ????????
    FRESULT res;             // ???????

    res = f_open(&file, "boot_count.txt", FA_READ); // ??boot_count.txt???
    if (res == FR_OK)                               // ?????
    {
        res = f_read(&file, &boot_count, sizeof(boot_count), &bytes_read); // ???????????
        if (res != FR_OK || bytes_read != sizeof(boot_count))              // ??????
        {
            boot_count = 0; // ?????????0
        }
        f_close(&file); // ??????
    }
    // ?????????????boot_count?????0

    return boot_count;
}

static data_storage_status_t save_boot_count_to_fatfs(uint32_t boot_count) // ??????????????FATFS ????:????????? ????:??????
{
    FIL file;               // ??????
    UINT bytes_written;     // ��???????
    FRESULT res;            // ???????

    res = f_open(&file, "boot_count.txt", FA_CREATE_ALWAYS | FA_WRITE); // ???????boot_count.txt???
    if (res != FR_OK)       // ?????
    {
        return DATA_STORAGE_ERROR;
    }

    res = f_write(&file, &boot_count, sizeof(boot_count), &bytes_written); // ��??????????
    if (res != FR_OK || bytes_written != sizeof(boot_count))               // ��?????
    {
        f_close(&file);
        return DATA_STORAGE_ERROR;
    }

    f_close(&file);         // ??????
    return DATA_STORAGE_OK;
}

static data_storage_status_t create_storage_directories(void) // ?????��?? ????:?? ????:??????
{
    FRESULT res;                  // ???????
    uint8_t success_count = 0;    // ???????

    for (uint8_t i = 0; i < STORAGE_TYPE_COUNT; i++) // ??????????
    {
        res = f_mkdir(g_directory_names[i]);          // ??????
        if (res == FR_OK)                             // ???????
        {
            // my_printf(&huart1, "Created directory: %s\r\n", g_directory_names[i]);
            success_count++;
        }
        else if (res == FR_EXIST) // ???????
        {
            // my_printf(&huart1, "Directory already exists: %s\r\n", g_directory_names[i]);
            success_count++;
        }
        else // ???????
        {
            // my_printf(&huart1, "Failed to create directory %s, error: %d\r\n", g_directory_names[i], res);
        }
    }

    return (success_count == STORAGE_TYPE_COUNT) ? DATA_STORAGE_OK : DATA_STORAGE_ERROR; // ?????????????????????????
}

data_storage_status_t data_storage_init(void) // ??????��?? ????:?? ????:??????
{
    memset(g_file_states, 0, sizeof(g_file_states)); // ?????????

    my_printf(&huart1, "Initializing data storage system...\r\n");

    FRESULT mount_result = f_mount(&SDFatFS, SDPath, 1); // ????SD???????
    if (mount_result != FR_OK)                           // ???????
    {
        my_printf(&huart1, "Failed to mount SD card filesystem, error: %d\r\n", mount_result);
        my_printf(&huart1, "Data storage system will work in degraded mode\r\n");
        return DATA_STORAGE_NO_SD;
    }

    my_printf(&huart1, "SD card filesystem mounted successfully\r\n");

    data_storage_status_t dir_result = create_storage_directories(); // ?????��??
    if (dir_result != DATA_STORAGE_OK)                               // ?????????
    {
        my_printf(&huart1, "Warning: Some directories creation failed, system may not work properly\r\n");
        // ??????��????????????????????
    }

    g_boot_count = get_boot_count_from_fatfs(); // ???????????
    g_boot_count++;

    data_storage_status_t boot_result = save_boot_count_to_fatfs(g_boot_count); // ????????????
    if (boot_result != DATA_STORAGE_OK)                                         // ???????
    {
        my_printf(&huart1, "Warning: Failed to save boot count\r\n");
    }

    my_printf(&huart1, "Data storage system initialized, boot count: %lu\r\n", g_boot_count); // ???????????

    return DATA_STORAGE_OK;
}

static data_storage_status_t check_and_update_filename(storage_type_t type) // ??��????????? ????:?��???? ????:??????
{
    if (type >= STORAGE_TYPE_COUNT) // ???????
    {
        return DATA_STORAGE_INVALID;
    }

    file_state_t *state = &g_file_states[type]; // ????????

    uint32_t max_records = app_config_get_storage_max_records(); // 使用统一配置
    if (state->data_count >= max_records || !state->file_exists) // 需要创建新文件
    {

        char filename[64];  // �ļ���������
        data_storage_status_t result = generate_filename(type, filename); // �����ļ���
        if (result != DATA_STORAGE_OK)
        {
            return result;
        }

        strcpy(state->current_filename, filename); // �����ļ���
        state->data_count = 0;
        state->file_exists = 1;

        // ������Ϣ�����ļ�׼��
        // my_printf(&huart1, "DEBUG: New file prepared, type=%d, filename=%s\r\n",
        //           type, filename);
    }

    return DATA_STORAGE_OK;
}

static data_storage_status_t write_data_to_file(storage_type_t type, const char *data) // ���ļ�д������ ����:�洢����,�����ַ��� ����:����״̬
{
    if (type >= STORAGE_TYPE_COUNT || data == NULL) // �������
    {
        return DATA_STORAGE_INVALID;
    }

    data_storage_status_t result = check_and_update_filename(type); // ��鲢�����ļ���
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    file_state_t *state = &g_file_states[type]; // ��ȡ�ļ�״̬

    // ���������ļ�·��
    char full_path[96];
    sprintf(full_path, "%s/%s", g_directory_names[type], state->current_filename);

    // ���ļ�д������(׷��ģʽ)
    FIL file_handle;
    FRESULT res = f_open(&file_handle, full_path, FA_OPEN_ALWAYS | FA_WRITE);
    if (res != FR_OK)
    {
        // ������Ϣ���ļ���ʧ��
        // my_printf(&huart1, "DEBUG: File open failed, type=%d, path=%s, res=%d\r\n",
        //           type, full_path, res);
        return DATA_STORAGE_ERROR;
    }

    // �ƶ����ļ�ĩβ(׷��ģʽ)
    res = f_lseek(&file_handle, f_size(&file_handle));
    if (res != FR_OK)
    {
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    UINT bytes_written;                                                           // д���ֽ���
    res = f_write(&file_handle, data, strlen(data), &bytes_written);             // д������
    if (res != FR_OK || bytes_written != strlen(data))                           // д��ʧ��
    {
        // ������Ϣ��д��ʧ��
        // my_printf(&huart1, "DEBUG: File write failed, type=%d, res=%d, expected=%d, written=%d\r\n",
        //           type, res, strlen(data), bytes_written);
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    res = f_write(&file_handle, "\n", 1, &bytes_written); // д�뻻�з�
    if (res != FR_OK || bytes_written != 1)               // ���з�д��ʧ��
    {
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    f_sync(&file_handle); // ͬ�����ݵ�SD��
    f_close(&file_handle); // �ر��ļ�

    state->data_count++; // �������ݼ���

    // ������Ϣ��д��ɹ�
    // my_printf(&huart1, "DEBUG: Data written successfully, type=%d, path=%s, count=%d\r\n",
    //           type, full_path, state->data_count);

    return DATA_STORAGE_OK;
}


static data_storage_status_t format_sample_data(float voltage, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %.1fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            voltage);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_sample(float voltage) // д��������� ����:��ѹֵ ����:����״̬
{
    char formatted_data[128]; // ��ʽ�����ݻ�����

    data_storage_status_t result = format_sample_data(voltage, formatted_data); // ��ʽ������
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_SAMPLE, formatted_data); // д���ļ�
}


static data_storage_status_t format_overlimit_data(float voltage, float limit, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %.0fV limit %.0fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            voltage,
            limit);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_overlimit(float voltage, float limit) // д�볬������ ����:��ѹֵ,��ֵ ����:����״̬
{
    char formatted_data[128]; // ��ʽ�����ݻ�����

    data_storage_status_t result = format_overlimit_data(voltage, limit, formatted_data); // ��ʽ������
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_OVERLIMIT, formatted_data); // д���ļ�
}


static data_storage_status_t format_log_data(const char *operation, char *formatted_data)
{
    if (formatted_data == NULL || operation == NULL)
    {
        return DATA_STORAGE_INVALID;
    }

    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %s",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            operation);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_log(const char *operation) // д����־���� ����:�������� ����:����״̬
{
    char formatted_data[256]; // ��ʽ�����ݻ�����

    data_storage_status_t result = format_log_data(operation, formatted_data); // ��ʽ������
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_LOG, formatted_data); // д���ļ�
}


static data_storage_status_t format_hidedata(float voltage, uint8_t is_overlimit, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    char original_line[128];
    sprintf(original_line, "%04d-%02d-%02d %02d:%02d:%02d %.1fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            voltage);

    
    uint32_t timestamp = convert_rtc_to_unix_timestamp(&current_rtc_time, &current_rtc_date);
    char hex_output[32];
    format_hex_output(timestamp, voltage, is_overlimit, hex_output);

    sprintf(formatted_data, "%s\nhide: %s", original_line, hex_output);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_hidedata(float voltage, uint8_t is_overlimit) // д���������� ����:��ѹֵ,���ޱ�־ ����:����״̬
{
    char formatted_data[256]; // ��ʽ�����ݻ�����

    data_storage_status_t result = format_hidedata(voltage, is_overlimit, formatted_data); // ��ʽ������
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_HIDEDATA, formatted_data); // д��hideDataĿ¼���������ĺ�ʮ������2�ָ�ʽ
}

data_storage_status_t generate_datetime_string(char *datetime_str) // ����datetime�ַ��� ����:�ַ��������� ����:����״̬
{
    if (datetime_str == NULL) // �������
    {
        return DATA_STORAGE_INVALID;
    }

    RTC_TimeTypeDef current_rtc_time = {0}; // ��ȡ��ǰRTCʱ��
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    sprintf(datetime_str, "%04d%02d%02d%02d%02d%02d", // ��ʽYYYYMMDDHHMMSS��ʽ(14λ����)
            current_rtc_date.Year + 2000,             // ���4λ����
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds);

    return DATA_STORAGE_OK;
}

data_storage_status_t storage_generate_filename(storage_type_t type, char *out_filename) // 生成文件名 参数:存储类型,文件名输出缓冲区 返回:操作状态
{
    if (out_filename == NULL || type >= STORAGE_TYPE_COUNT) // 参数验证
    {
        return DATA_STORAGE_INVALID;
    }

    if (type == STORAGE_LOG) // log类型使用启动计数命名：log{boot_count}.txt
    {
        sprintf(out_filename, "%s%lu.txt", g_storage_filename_prefixes[type], g_boot_count);
    }
    else // 其他类型使用datetime命名：{prefix}{datetime}.txt
    {
        char datetime_string[16];                                                      // 日期时间字符串
        data_storage_status_t result = storage_generate_datetime_string(datetime_string); // 生成时间字符串
        if (result != DATA_STORAGE_OK)
        {
            return result;
        }
        sprintf(out_filename, "%s%s.txt", g_storage_filename_prefixes[type], datetime_string);
    }

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_test(void) // ���Դ洢ϵͳ ����:�� ����:����״̬
{
    my_printf(&huart1, "Data storage system test - placeholder\r\n");
    return DATA_STORAGE_OK;
}
