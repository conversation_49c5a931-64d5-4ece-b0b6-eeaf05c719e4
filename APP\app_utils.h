// 文件名：app_utils.h
// 功能：应用层公共工具头文件，提供通用的工具函数和宏定义
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __APP_UTILS_H__
#define __APP_UTILS_H__

#include "stdint.h"
#include "app_config.h"

// ========== 通用初始化模式宏 ==========
#define APP_INIT_PATTERN(module_name, init_flag, init_func) \
    do { \
        if (init_flag) return APP_CONFIG_OK; \
        app_config_status_t status = init_func(); \
        if (status == APP_CONFIG_OK) init_flag = 1; \
        return status; \
    } while(0)

#define APP_RESET_PATTERN(module_name, init_flag, init_func) \
    do { \
        init_flag = 0; \
        return init_func(); \
    } while(0)

#define APP_CHECK_INIT_PATTERN(init_flag) \
    do { \
        if (!init_flag) return APP_CONFIG_NOT_INITIALIZED; \
    } while(0)

// ========== 参数验证宏 ==========
#define APP_VALIDATE_POINTER(ptr) \
    do { \
        if (!(ptr)) return APP_CONFIG_INVALID_PARAM; \
    } while(0)

#define APP_VALIDATE_RANGE_RETURN(val, min, max, ret_val) \
    do { \
        if (!APP_CONFIG_VALIDATE_RANGE(val, min, max)) return ret_val; \
    } while(0)

#define APP_VALIDATE_BUFFER_SIZE_RETURN(size, ret_val) \
    do { \
        if (!APP_CONFIG_VALIDATE_BUFFER_SIZE(size)) return ret_val; \
    } while(0)

// ========== 错误处理宏 ==========
#define APP_HANDLE_ERROR(condition, error_code, action) \
    do { \
        if (condition) { \
            action; \
            return error_code; \
        } \
    } while(0)

#define APP_HANDLE_STATUS(status_var, func_call, error_action) \
    do { \
        status_var = func_call; \
        if (status_var != APP_CONFIG_OK) { \
            error_action; \
            return status_var; \
        } \
    } while(0)

// ========== 循环优化宏 ==========
#define APP_FOR_EACH_VALIDATE(array, count, validate_func, error_ret) \
    do { \
        for (uint32_t i = 0; i < count; i++) { \
            if (!validate_func(array[i])) return error_ret; \
        } \
    } while(0)

#define APP_FOR_EACH_INIT(array, count, init_func) \
    do { \
        for (uint32_t i = 0; i < count; i++) { \
            init_func(&array[i]); \
        } \
    } while(0)

// ========== 字符串处理工具 ==========
typedef struct {
    char* buffer;                           // 字符串缓冲区
    uint32_t size;                          // 缓冲区大小
    uint32_t length;                        // 当前字符串长度
} app_string_buffer_t;

// ========== 时间戳工具 ==========
typedef struct {
    uint32_t timestamp;                     // 时间戳
    uint32_t duration;                      // 持续时间
    uint8_t active;                         // 激活标志
} app_timer_t;

// ========== 状态机工具 ==========
typedef enum {
    APP_STATE_MACHINE_IDLE = 0,             // 空闲状态
    APP_STATE_MACHINE_RUNNING = 1,          // 运行状态
    APP_STATE_MACHINE_PAUSED = 2,           // 暂停状态
    APP_STATE_MACHINE_ERROR = 3             // 错误状态
} app_state_machine_state_t;

typedef struct {
    app_state_machine_state_t current_state; // 当前状态
    app_state_machine_state_t previous_state; // 前一状态
    uint32_t state_enter_time;              // 状态进入时间
    uint32_t state_duration;                // 状态持续时间
} app_state_machine_t;

// ========== 公共工具函数声明 ==========
// 初始化工具
app_config_status_t app_utils_init(void);                                     // 初始化工具模块
uint8_t app_utils_is_initialized(void);                                       // 检查是否已初始化

// 参数验证工具
uint8_t app_utils_validate_pointer(const void* ptr);                          // 验证指针有效性
uint8_t app_utils_validate_range_uint32(uint32_t val, uint32_t min, uint32_t max); // 验证uint32范围
uint8_t app_utils_validate_range_float(float val, float min, float max);      // 验证float范围
uint8_t app_utils_validate_buffer_size(uint32_t size);                        // 验证缓冲区大小

// 字符串处理工具
app_config_status_t app_utils_string_init(app_string_buffer_t* str_buf, char* buffer, uint32_t size); // 初始化字符串缓冲区
app_config_status_t app_utils_string_append(app_string_buffer_t* str_buf, const char* text); // 追加字符串
app_config_status_t app_utils_string_format(app_string_buffer_t* str_buf, const char* format, ...); // 格式化字符串
app_config_status_t app_utils_string_clear(app_string_buffer_t* str_buf);     // 清空字符串
uint32_t app_utils_string_length(const app_string_buffer_t* str_buf);         // 获取字符串长度

// 时间戳工具
app_config_status_t app_utils_timer_init(app_timer_t* timer, uint32_t duration); // 初始化定时器
app_config_status_t app_utils_timer_start(app_timer_t* timer);                // 启动定时器
app_config_status_t app_utils_timer_stop(app_timer_t* timer);                 // 停止定时器
uint8_t app_utils_timer_is_expired(const app_timer_t* timer);                 // 检查定时器是否过期
uint32_t app_utils_timer_get_elapsed(const app_timer_t* timer);               // 获取已过时间

// 状态机工具
app_config_status_t app_utils_state_machine_init(app_state_machine_t* sm);    // 初始化状态机
app_config_status_t app_utils_state_machine_set_state(app_state_machine_t* sm, app_state_machine_state_t new_state); // 设置状态
app_state_machine_state_t app_utils_state_machine_get_state(const app_state_machine_t* sm); // 获取当前状态
uint32_t app_utils_state_machine_get_duration(const app_state_machine_t* sm); // 获取状态持续时间

// 数组处理工具
app_config_status_t app_utils_array_init_uint8(uint8_t* array, uint32_t count, uint8_t init_value); // 初始化uint8数组
app_config_status_t app_utils_array_init_uint32(uint32_t* array, uint32_t count, uint32_t init_value); // 初始化uint32数组
app_config_status_t app_utils_array_init_float(float* array, uint32_t count, float init_value); // 初始化float数组
uint8_t app_utils_array_validate_uint8(const uint8_t* array, uint32_t count, uint8_t min, uint8_t max); // 验证uint8数组
uint8_t app_utils_array_validate_float(const float* array, uint32_t count, float min, float max); // 验证float数组

// 配置获取工具
app_config_status_t app_utils_get_config_with_default(float* result, 
                                                      float default_value,
                                                      app_config_status_t (*get_func)(float*)); // 获取配置值(带默认值)
app_config_status_t app_utils_get_limit_value(float* limit_value);             // 获取限值配置
app_config_status_t app_utils_get_ratio_value(float* ratio_value);             // 获取比率配置

// 错误处理工具
const char* app_utils_get_error_string(app_config_status_t status);           // 获取错误描述字符串
app_config_status_t app_utils_log_error(app_config_status_t status, const char* context); // 记录错误日志

// 数学工具
float app_utils_clamp_float(float value, float min, float max);               // 限制float值范围
uint32_t app_utils_clamp_uint32(uint32_t value, uint32_t min, uint32_t max);  // 限制uint32值范围
uint8_t app_utils_is_power_of_two(uint32_t value);                            // 检查是否为2的幂

#endif // __APP_UTILS_H__
