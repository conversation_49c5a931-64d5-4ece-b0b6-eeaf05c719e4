

#include "sampling_control.h"
#include "stddef.h"
#include "config_manager.h"
#include "data_storage.h"
#include "usart_app.h"
#include "adc_app.h"
#include "app_config.h"     // 统一配置管理

static sampling_control_t g_sampling_control = {0}; 
static uint8_t g_sampling_initialized = 0;        

// LED闪烁周期现在通过统一配置管理，不再需要本地定义

sampling_status_t sampling_init(void) 
{
    if (g_sampling_initialized) 
    {
        return SAMPLING_OK;
    }

    config_init();

    g_sampling_control.state = SAMPLING_IDLE;                       
    g_sampling_control.cycle = config_get_sampling_cycle();        
    g_sampling_control.last_sample_time = 0;                        
    g_sampling_control.led_blink_time = 0;                          
    g_sampling_control.led_blink_state = 0;                        

    g_sampling_initialized = 1; 
    return SAMPLING_OK;
}

sampling_status_t sampling_start(void) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_ACTIVE;
    g_sampling_control.last_sample_time = HAL_GetTick();           
    g_sampling_control.led_blink_time = HAL_GetTick();              
    g_sampling_control.led_blink_state = 0;                         

    return SAMPLING_OK;
}

sampling_status_t sampling_stop(void) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_IDLE;      
    g_sampling_control.led_blink_state = 0;       
    return SAMPLING_OK;
}

sampling_status_t sampling_set_cycle(sampling_cycle_t cycle) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_ERROR;

    if (cycle != CYCLE_5S && cycle != CYCLE_10S && cycle != CYCLE_15S) 
    {
        return SAMPLING_INVALID;
    }

    g_sampling_control.cycle = cycle; 

    if (config_set_sampling_cycle(cycle) == CONFIG_OK) 
    {
        config_save_to_flash();
    }

    return SAMPLING_OK;
}

sampling_state_t sampling_get_state(void) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_IDLE;
    return g_sampling_control.state;
}

sampling_cycle_t sampling_get_cycle(void) 
{
    if (!g_sampling_initialized) 
        return CYCLE_5S;
    return g_sampling_control.cycle;
}

uint8_t sampling_should_sample(void) 
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) 
    {
        return 0;
    }

    uint32_t current_time = HAL_GetTick();                                       // ��ȡ��ǰʱ��
    uint32_t elapsed_time = current_time - g_sampling_control.last_sample_time; // ����ʱ����
    uint32_t cycle_ms = g_sampling_control.cycle * 1000;                        // ת��Ϊ����

    return (elapsed_time >= cycle_ms) ? 1 : 0; // �ж��Ƿ񵽴����ʱ��
}

void sampling_update_led_blink(void) // ����LED��˸״̬ ����:�� ����:��
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) // ����ʼ����״̬
    {
        g_sampling_control.led_blink_state = 0; // ����ֹͣʱLEDϨ��
        return;
    }

    uint32_t current_time = HAL_GetTick();                                      // ��ȡ��ǰʱ��
    uint32_t elapsed_time = current_time - g_sampling_control.led_blink_time;  // ������˸���

    uint32_t blink_period = app_config_get_led_blink_period(); // 使用统一配置
    if (elapsed_time >= blink_period) // 检查是否到达闪烁周期
    {
        g_sampling_control.led_blink_state ^= 1;            // ��תLED״̬
        g_sampling_control.led_blink_time = current_time;   // ������˸ʱ��
    }
}

float sampling_get_voltage(void) // 获取当前电压值，使用统一状态管理
{
    float voltage = app_state_get_current_voltage(); // 从统一状态管理获取电压
    config_params_t config_params; // 配置参数结构体

    if (config_get_params(&config_params) != CONFIG_OK) // 获取配置参数
    {
        return voltage; // 如果获取失败时返回原始电压值
    }

    return voltage * config_params.ratio; // 使用ratio参数计算电压值
}

uint8_t sampling_check_overlimit(void) // ����Ƿ��� ����:�� ����:���ޱ�־
{
    config_params_t config_params; // ���ò����ṹ��

    if (config_get_params(&config_params) != CONFIG_OK) // ��ȡ���ò���
    {
        return 0; // ���û�ȡʧ��ʱ��Ϊδ����
    }

    float current_voltage = sampling_get_voltage(); // ��ȡ��ǰ��ѹֵ

    return (current_voltage > config_params.limit) ? 1 : 0; // ����Ƿ񳬹�limit��ֵ
}

void sampling_task(void) // ���������� ����:�� ����:��
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return;

    sampling_update_led_blink(); // ����LED��˸״̬

    if (g_sampling_control.state == SAMPLING_ACTIVE) // ������ڲ���״̬������Ƿ���Ҫ���в���
    {
        if (sampling_should_sample()) // ����Ƿ񵽴����ʱ��
        {
            g_sampling_control.last_sample_time = HAL_GetTick(); // ���²���ʱ���

            float current_voltage = sampling_get_voltage();      // ��ȡ��ǰ��ѹֵ
            uint8_t is_overlimit = sampling_check_overlimit();   // ����Ƿ���

            // �������ݴ洢��������
            sampling_handle_data_storage(current_voltage, is_overlimit);
        }
    }
}

uint8_t sampling_get_led_blink_state(void) // ��ȡLED��˸״̬ ����:�� ����:��˸״̬
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return 0;
    return g_sampling_control.led_blink_state;
}

void sampling_handle_data_storage(float voltage, uint8_t is_overlimit) // 处理数据存储，使用事件驱动
{
    // 发布采样数据事件，让存储模块订阅处理
    app_events_publish_sampling_data(voltage, is_overlimit);

    // 如果超限，发布超限事件
    if (is_overlimit) {
        app_events_publish_overlimit(voltage);
    }
}
