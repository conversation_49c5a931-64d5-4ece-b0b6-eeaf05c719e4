

#include "sampling_control.h"
#include "stddef.h"
#include "config_manager.h"
#include "data_storage.h"
#include "usart_app.h"
#include "adc_app.h"
#include "app_config.h"     // 统一配置管理

static sampling_control_t g_sampling_control = {0}; 
static uint8_t g_sampling_initialized = 0;        

// LED闪烁周期现在通过统一配置管理，不再需要本地定义

sampling_status_t sampling_init(void) 
{
    if (g_sampling_initialized) 
    {
        return SAMPLING_OK;
    }

    config_init();

    g_sampling_control.state = SAMPLING_IDLE;                       
    g_sampling_control.cycle = config_get_sampling_cycle();        
    g_sampling_control.last_sample_time = 0;                        
    g_sampling_control.led_blink_time = 0;                          
    g_sampling_control.led_blink_state = 0;                        

    g_sampling_initialized = 1; 
    return SAMPLING_OK;
}

sampling_status_t sampling_start(void) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_ACTIVE;
    g_sampling_control.last_sample_time = HAL_GetTick();           
    g_sampling_control.led_blink_time = HAL_GetTick();              
    g_sampling_control.led_blink_state = 0;                         

    return SAMPLING_OK;
}

sampling_status_t sampling_stop(void) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_IDLE;      
    g_sampling_control.led_blink_state = 0;       
    return SAMPLING_OK;
}

sampling_status_t sampling_set_cycle(sampling_cycle_t cycle) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_ERROR;

    if (cycle != CYCLE_5S && cycle != CYCLE_10S && cycle != CYCLE_15S) 
    {
        return SAMPLING_INVALID;
    }

    g_sampling_control.cycle = cycle; 

    if (config_set_sampling_cycle(cycle) == CONFIG_OK) 
    {
        config_save_to_flash();
    }

    return SAMPLING_OK;
}

sampling_state_t sampling_get_state(void) 
{
    if (!g_sampling_initialized) 
        return SAMPLING_IDLE;
    return g_sampling_control.state;
}

sampling_cycle_t sampling_get_cycle(void) 
{
    if (!g_sampling_initialized) 
        return CYCLE_5S;
    return g_sampling_control.cycle;
}

uint8_t sampling_should_sample(void) 
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) 
    {
        return 0;
    }

    uint32_t current_time = HAL_GetTick();                                       // ��ȡ��ǰʱ��
    uint32_t elapsed_time = current_time - g_sampling_control.last_sample_time; // ����ʱ����
    uint32_t cycle_ms = g_sampling_control.cycle * 1000;                        // ת��Ϊ����

    return (elapsed_time >= cycle_ms) ? 1 : 0; // �ж��Ƿ񵽴����ʱ��
}

void sampling_update_led_blink(void) // ����LED��˸״̬ ����:�� ����:��
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) // ����ʼ����״̬
    {
        g_sampling_control.led_blink_state = 0; // ����ֹͣʱLEDϨ��
        return;
    }

    uint32_t current_time = HAL_GetTick();                                      // ��ȡ��ǰʱ��
    uint32_t elapsed_time = current_time - g_sampling_control.led_blink_time;  // ������˸���

    uint32_t blink_period = app_config_get_led_blink_period(); // 使用统一配置
    if (elapsed_time >= blink_period) // 检查是否到达闪烁周期
    {
        g_sampling_control.led_blink_state ^= 1;            // ��תLED״̬
        g_sampling_control.led_blink_time = current_time;   // ������˸ʱ��
    }
}

float sampling_get_voltage(void) // ��ȡ��ǰ��ѹֵ ����:�� ����:��ѹֵ
{
    extern __IO float voltage;     // ����adc_app.c��ȫ�ֵ�ѹ����
    config_params_t config_params; // ���ò����ṹ��

    if (config_get_params(&config_params) != CONFIG_OK) // ��ȡ���ò���
    {
        return voltage; // ���û�ȡʧ��ʱ����ԭʼ��ѹֵ
    }

    return voltage * config_params.ratio; // ʹ��ratio�������е�ѹ����
}

uint8_t sampling_check_overlimit(void) // ����Ƿ��� ����:�� ����:���ޱ�־
{
    config_params_t config_params; // ���ò����ṹ��

    if (config_get_params(&config_params) != CONFIG_OK) // ��ȡ���ò���
    {
        return 0; // ���û�ȡʧ��ʱ��Ϊδ����
    }

    float current_voltage = sampling_get_voltage(); // ��ȡ��ǰ��ѹֵ

    return (current_voltage > config_params.limit) ? 1 : 0; // ����Ƿ񳬹�limit��ֵ
}

void sampling_task(void) // ���������� ����:�� ����:��
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return;

    sampling_update_led_blink(); // ����LED��˸״̬

    if (g_sampling_control.state == SAMPLING_ACTIVE) // ������ڲ���״̬������Ƿ���Ҫ���в���
    {
        if (sampling_should_sample()) // ����Ƿ񵽴����ʱ��
        {
            g_sampling_control.last_sample_time = HAL_GetTick(); // ���²���ʱ���

            float current_voltage = sampling_get_voltage();      // ��ȡ��ǰ��ѹֵ
            uint8_t is_overlimit = sampling_check_overlimit();   // ����Ƿ���

            // �������ݴ洢��������
            sampling_handle_data_storage(current_voltage, is_overlimit);
        }
    }
}

uint8_t sampling_get_led_blink_state(void) // ��ȡLED��˸״̬ ����:�� ����:��˸״̬
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return 0;
    return g_sampling_control.led_blink_state;
}

void sampling_handle_data_storage(float voltage, uint8_t is_overlimit) // �������ݴ洢 ����:��ѹֵ,���ޱ�־ ����:��
{
    // ��ȡ��ǰ�����ʽ
    extern output_format_t g_output_format; // ����usart_app.c��ȫ�ֱ���

    // ���ݴ洢���������������ʽѡ��洢��ʽ
    if (g_output_format == OUTPUT_FORMAT_HIDDEN)
    {
        // ����ģʽ���洢��hideData�ļ���
        data_storage_status_t result = data_storage_write_hidedata(voltage, is_overlimit);
        if (result != DATA_STORAGE_OK)
        {
            // �洢ʧ��ʱ�����������Ϣ���������������
            // �������������Ӵ������߼�
        }
    }
    else
    {
        // ����ģʽ���洢��sample�ļ���
        data_storage_status_t result = data_storage_write_sample(voltage);
        if (result != DATA_STORAGE_OK)
        {
            // �洢ʧ��ʱ�����������Ϣ���������������
            // �������������Ӵ������߼�
        }
    }

    // �������ݶ���洢��overLimit�ļ��У�����hideģʽ��
    if (is_overlimit)
    {
        config_params_t config_params;
        float limit_value = 0.0f;

        // ��ȡlimit��ֵ���ڴ洢
        if (config_get_params(&config_params) == CONFIG_OK)
        {
            limit_value = config_params.limit;
        }

        data_storage_status_t result = data_storage_write_overlimit(voltage, limit_value);
        if (result != DATA_STORAGE_OK)
        {
            // �洢ʧ��ʱ�����������Ϣ���������������
            // �������������Ӵ������߼�
        }
    }
}
