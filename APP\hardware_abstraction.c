// 文件名：hardware_abstraction.c
// 功能：硬件抽象层实现，提供统一的硬件访问服务
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#include "hardware_abstraction.h"
#include "main.h"
#include "gpio.h"
#include "adc.h"
#include "usart.h"
#include "rtc.h"
#include "tim.h"
#include "app_utils.h"

// ========== 内部状态管理 ==========
static uint8_t g_hal_initialized = 0;
static hal_adc_mode_t g_current_adc_mode = HAL_ADC_MODE_SINGLE;

// ========== 外部硬件句柄引用 ==========
extern ADC_HandleTypeDef hadc1;
extern UART_HandleTypeDef huart1;
extern RTC_HandleTypeDef hrtc;
extern TIM_HandleTypeDef htim3;

// ========== GPIO抽象接口实现 ==========
hal_status_t hal_gpio_init(void) // 初始化GPIO
{
    // GPIO已在MX_GPIO_Init()中初始化，这里只做验证
    return HAL_STATUS_OK;
}

hal_status_t hal_gpio_set_led(hal_led_index_t led_index, hal_gpio_pin_state_t state) // 设置LED状态
{
    if (led_index >= HAL_LED_COUNT) return HAL_STATUS_INVALID_PARAM;
    
    GPIO_TypeDef* gpio_port = GPIOD;
    uint16_t gpio_pin;
    
    // 根据LED索引确定GPIO引脚
    switch (led_index) {
        case HAL_LED_0: gpio_pin = GPIO_PIN_8; break;
        case HAL_LED_1: gpio_pin = GPIO_PIN_9; break;
        case HAL_LED_2: gpio_pin = GPIO_PIN_10; break;
        case HAL_LED_3: gpio_pin = GPIO_PIN_11; break;
        case HAL_LED_4: gpio_pin = GPIO_PIN_12; break;
        case HAL_LED_5: gpio_pin = GPIO_PIN_13; break;
        default: return HAL_STATUS_INVALID_PARAM;
    }
    
    GPIO_PinState pin_state = (state == HAL_GPIO_PIN_SET) ? GPIO_PIN_SET : GPIO_PIN_RESET;
    HAL_GPIO_WritePin(gpio_port, gpio_pin, pin_state);
    
    return HAL_STATUS_OK;
}

hal_status_t hal_gpio_toggle_led(hal_led_index_t led_index) // 翻转LED状态
{
    if (led_index >= HAL_LED_COUNT) return HAL_STATUS_INVALID_PARAM;
    
    GPIO_TypeDef* gpio_port = GPIOD;
    uint16_t gpio_pin;
    
    switch (led_index) {
        case HAL_LED_0: gpio_pin = GPIO_PIN_8; break;
        case HAL_LED_1: gpio_pin = GPIO_PIN_9; break;
        case HAL_LED_2: gpio_pin = GPIO_PIN_10; break;
        case HAL_LED_3: gpio_pin = GPIO_PIN_11; break;
        case HAL_LED_4: gpio_pin = GPIO_PIN_12; break;
        case HAL_LED_5: gpio_pin = GPIO_PIN_13; break;
        default: return HAL_STATUS_INVALID_PARAM;
    }
    
    HAL_GPIO_TogglePin(gpio_port, gpio_pin);
    return HAL_STATUS_OK;
}

hal_gpio_pin_state_t hal_gpio_get_led_state(hal_led_index_t led_index) // 获取LED状态
{
    if (led_index >= HAL_LED_COUNT) return HAL_GPIO_PIN_RESET;
    
    GPIO_TypeDef* gpio_port = GPIOD;
    uint16_t gpio_pin;
    
    switch (led_index) {
        case HAL_LED_0: gpio_pin = GPIO_PIN_8; break;
        case HAL_LED_1: gpio_pin = GPIO_PIN_9; break;
        case HAL_LED_2: gpio_pin = GPIO_PIN_10; break;
        case HAL_LED_3: gpio_pin = GPIO_PIN_11; break;
        case HAL_LED_4: gpio_pin = GPIO_PIN_12; break;
        case HAL_LED_5: gpio_pin = GPIO_PIN_13; break;
        default: return HAL_GPIO_PIN_RESET;
    }
    
    GPIO_PinState pin_state = HAL_GPIO_ReadPin(gpio_port, gpio_pin);
    return (pin_state == GPIO_PIN_SET) ? HAL_GPIO_PIN_SET : HAL_GPIO_PIN_RESET;
}

// ========== ADC抽象接口实现 ==========
hal_status_t hal_adc_init(hal_adc_mode_t mode) // 初始化ADC
{
    g_current_adc_mode = mode;
    // ADC已在MX_ADC1_Init()中初始化
    return HAL_STATUS_OK;
}

hal_status_t hal_adc_start_conversion(void) // 开始ADC转换
{
    HAL_StatusTypeDef hal_result;
    
    switch (g_current_adc_mode) {
        case HAL_ADC_MODE_SINGLE:
            hal_result = HAL_ADC_Start(&hadc1);
            break;
        case HAL_ADC_MODE_CONTINUOUS:
            hal_result = HAL_ADC_Start(&hadc1);
            break;
        case HAL_ADC_MODE_DMA:
            // DMA模式需要缓冲区，这里简化处理
            hal_result = HAL_ADC_Start(&hadc1);
            break;
        default:
            return HAL_STATUS_INVALID_PARAM;
    }
    
    return (hal_result == HAL_OK) ? HAL_STATUS_OK : HAL_STATUS_ERROR;
}

hal_status_t hal_adc_get_result(hal_adc_result_t* out_result) // 获取ADC结果
{
    APP_VALIDATE_POINTER(out_result);
    
    HAL_StatusTypeDef hal_result = HAL_ADC_PollForConversion(&hadc1, 1000);
    if (hal_result != HAL_OK) {
        return (hal_result == HAL_TIMEOUT) ? HAL_STATUS_TIMEOUT : HAL_STATUS_ERROR;
    }
    
    out_result->raw_value = HAL_ADC_GetValue(&hadc1);
    out_result->voltage = (float)out_result->raw_value * app_config_get_adc_reference_voltage() / APP_ADC_MAX_VALUE;
    out_result->timestamp = HAL_GetTick();
    out_result->channel = 0; // 简化处理，假设使用通道0
    
    return HAL_STATUS_OK;
}

hal_status_t hal_adc_stop_conversion(void) // 停止ADC转换
{
    HAL_StatusTypeDef hal_result = HAL_ADC_Stop(&hadc1);
    return (hal_result == HAL_OK) ? HAL_STATUS_OK : HAL_STATUS_ERROR;
}

uint8_t hal_adc_is_conversion_complete(void) // 检查转换是否完成
{
    return __HAL_ADC_GET_FLAG(&hadc1, ADC_FLAG_EOC) ? 1 : 0;
}

// ========== UART抽象接口实现 ==========
hal_status_t hal_uart_init(const hal_uart_config_t* config) // 初始化UART
{
    APP_VALIDATE_POINTER(config);
    
    // UART已在MX_USART1_UART_Init()中初始化，这里可以重新配置
    huart1.Init.BaudRate = config->baudrate;
    
    HAL_StatusTypeDef hal_result = HAL_UART_Init(&huart1);
    return (hal_result == HAL_OK) ? HAL_STATUS_OK : HAL_STATUS_ERROR;
}

hal_status_t hal_uart_transmit(const uint8_t* data, uint16_t length, uint32_t timeout) // 发送数据
{
    APP_VALIDATE_POINTER(data);
    
    HAL_StatusTypeDef hal_result = HAL_UART_Transmit(&huart1, (uint8_t*)data, length, timeout);
    
    switch (hal_result) {
        case HAL_OK: return HAL_STATUS_OK;
        case HAL_TIMEOUT: return HAL_STATUS_TIMEOUT;
        case HAL_BUSY: return HAL_STATUS_BUSY;
        default: return HAL_STATUS_ERROR;
    }
}

hal_status_t hal_uart_receive(uint8_t* data, uint16_t length, uint32_t timeout) // 接收数据
{
    APP_VALIDATE_POINTER(data);
    
    HAL_StatusTypeDef hal_result = HAL_UART_Receive(&huart1, data, length, timeout);
    
    switch (hal_result) {
        case HAL_OK: return HAL_STATUS_OK;
        case HAL_TIMEOUT: return HAL_STATUS_TIMEOUT;
        case HAL_BUSY: return HAL_STATUS_BUSY;
        default: return HAL_STATUS_ERROR;
    }
}

hal_status_t hal_uart_transmit_dma(const uint8_t* data, uint16_t length) // DMA发送
{
    APP_VALIDATE_POINTER(data);
    
    HAL_StatusTypeDef hal_result = HAL_UART_Transmit_DMA(&huart1, (uint8_t*)data, length);
    return (hal_result == HAL_OK) ? HAL_STATUS_OK : HAL_STATUS_ERROR;
}

hal_status_t hal_uart_receive_dma(uint8_t* data, uint16_t length) // DMA接收
{
    APP_VALIDATE_POINTER(data);
    
    HAL_StatusTypeDef hal_result = HAL_UART_Receive_DMA(&huart1, data, length);
    return (hal_result == HAL_OK) ? HAL_STATUS_OK : HAL_STATUS_ERROR;
}

uint16_t hal_uart_get_received_count(void) // 获取已接收字节数
{
    // 简化实现，实际应用中需要根据DMA状态计算
    return 0;
}

// ========== RTC抽象接口实现 ==========
hal_status_t hal_rtc_init(void) // 初始化RTC
{
    // RTC已在MX_RTC_Init()中初始化
    return HAL_STATUS_OK;
}

hal_status_t hal_rtc_set_time(const hal_rtc_time_t* time) // 设置时间
{
    APP_VALIDATE_POINTER(time);
    
    RTC_TimeTypeDef rtc_time = {0};
    rtc_time.Hours = time->hours;
    rtc_time.Minutes = time->minutes;
    rtc_time.Seconds = time->seconds;
    rtc_time.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    rtc_time.StoreOperation = RTC_STOREOPERATION_RESET;
    
    HAL_StatusTypeDef hal_result = HAL_RTC_SetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
    return (hal_result == HAL_OK) ? HAL_STATUS_OK : HAL_STATUS_ERROR;
}

hal_status_t hal_rtc_get_time(hal_rtc_time_t* out_time) // 获取时间
{
    APP_VALIDATE_POINTER(out_time);
    
    RTC_TimeTypeDef rtc_time = {0};
    HAL_StatusTypeDef hal_result = HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
    
    if (hal_result == HAL_OK) {
        out_time->hours = rtc_time.Hours;
        out_time->minutes = rtc_time.Minutes;
        out_time->seconds = rtc_time.Seconds;
        return HAL_STATUS_OK;
    }
    
    return HAL_STATUS_ERROR;
}

hal_status_t hal_rtc_set_date(const hal_rtc_date_t* date) // 设置日期
{
    APP_VALIDATE_POINTER(date);
    
    RTC_DateTypeDef rtc_date = {0};
    rtc_date.Year = date->year;
    rtc_date.Month = date->month;
    rtc_date.Date = date->day;
    rtc_date.WeekDay = date->weekday;
    
    HAL_StatusTypeDef hal_result = HAL_RTC_SetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);
    return (hal_result == HAL_OK) ? HAL_STATUS_OK : HAL_STATUS_ERROR;
}

hal_status_t hal_rtc_get_date(hal_rtc_date_t* out_date) // 获取日期
{
    APP_VALIDATE_POINTER(out_date);
    
    RTC_DateTypeDef rtc_date = {0};
    HAL_StatusTypeDef hal_result = HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);
    
    if (hal_result == HAL_OK) {
        out_date->year = rtc_date.Year;
        out_date->month = rtc_date.Month;
        out_date->day = rtc_date.Date;
        out_date->weekday = rtc_date.WeekDay;
        return HAL_STATUS_OK;
    }
    
    return HAL_STATUS_ERROR;
}

uint32_t hal_rtc_get_timestamp(void) // 获取时间戳
{
    // 简化实现，返回系统时钟计数
    return HAL_GetTick();
}

// ========== 定时器抽象接口实现 ==========
hal_status_t hal_timer_init(void) // 初始化定时器
{
    // 定时器已在MX_TIM3_Init()中初始化
    return HAL_STATUS_OK;
}

uint32_t hal_timer_get_tick(void) // 获取系统时钟计数
{
    return HAL_GetTick();
}

hal_status_t hal_timer_delay(uint32_t delay_ms) // 延时函数
{
    HAL_Delay(delay_ms);
    return HAL_STATUS_OK;
}

// ========== 硬件抽象层管理接口实现 ==========
hal_status_t hal_init_all(void) // 初始化所有硬件
{
    if (g_hal_initialized) return HAL_STATUS_OK;
    
    hal_status_t status;
    
    APP_HANDLE_STATUS(status, hal_gpio_init(), /* no action */);
    APP_HANDLE_STATUS(status, hal_adc_init(HAL_ADC_MODE_SINGLE), /* no action */);
    APP_HANDLE_STATUS(status, hal_rtc_init(), /* no action */);
    APP_HANDLE_STATUS(status, hal_timer_init(), /* no action */);
    
    g_hal_initialized = 1;
    return HAL_STATUS_OK;
}

hal_status_t hal_deinit_all(void) // 反初始化所有硬件
{
    g_hal_initialized = 0;
    return HAL_STATUS_OK;
}

const char* hal_get_status_string(hal_status_t status) // 获取状态字符串
{
    switch (status) {
        case HAL_STATUS_OK:              return "HAL OK";
        case HAL_STATUS_ERROR:           return "HAL Error";
        case HAL_STATUS_BUSY:            return "HAL Busy";
        case HAL_STATUS_TIMEOUT:         return "HAL Timeout";
        case HAL_STATUS_NOT_INITIALIZED: return "HAL Not Initialized";
        case HAL_STATUS_INVALID_PARAM:   return "HAL Invalid Parameter";
        default:                         return "HAL Unknown Status";
    }
}

hal_status_t hal_self_test(void) // 硬件自检
{
    if (!g_hal_initialized) return HAL_STATUS_NOT_INITIALIZED;
    
    // 简化的自检实现
    hal_adc_result_t adc_result;
    hal_status_t status = hal_adc_get_result(&adc_result);
    if (status != HAL_STATUS_OK) return status;
    
    hal_rtc_time_t rtc_time;
    status = hal_rtc_get_time(&rtc_time);
    if (status != HAL_STATUS_OK) return status;
    
    return HAL_STATUS_OK;
}
