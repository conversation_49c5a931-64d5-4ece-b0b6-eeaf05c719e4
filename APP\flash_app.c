#include"flash_app.h"
#include"fatfs.h"
#include"ff.h"
#include<string.h>
#include<stdlib.h>



lfs_t lfs;
struct lfs_config cfg;

void list_dir_recursive(const char*path, int level)
{
    lfs_dir_t dir;
    struct lfs_info info;
    char full_path[128];

    if(lfs_dir_open(&lfs,&dir, path)!= LFS_ERR_OK)
    {
        my_printf(&huart1,"无法打开目录:%s\r\n", path);
        return;
    }
    
    while(true)
    {
        int res = lfs_dir_read(&lfs,&dir,&info);
        if(res <= 0)
        {
            break;
        }
        
        if(strcmp(info.name,".")== 0 || strcmp(info.name,"..")== 0)
        {
            continue;
        }
        
        for(int i=0; i<level; i++)
        {
            my_printf(&huart1,"    ");
        }
        
        if(info.type == LFS_TYPE_DIR)
        {
            my_printf(&huart1,"+--[DIR]%s\r\n",info.name);
            if(strcmp(path,"/") == 0)
            {
                sprintf(full_path,"/%s",info.name);
            }
            else
            {
                sprintf(full_path,"%s/%s",path,info.name);
            }
            list_dir_recursive(full_path, level+1);
        }
        else
        {
            my_printf(&huart1,"+--[FILE]%s(%lu bytes)\r\n", info.name,(unsigned long)info.size);
        }
    }
    
    lfs_dir_close(&lfs,&dir);
}

void lfs_basic_test(void)
{
    my_printf(&huart1,"\r\n--- LittleFS文件系统测试---\r\n");
    
    int err = lfs_mount(&lfs,&cfg);
    if(err)
    {
        my_printf(&huart1,"LFS:挂载失败(%d),正在格式化...\n",err);
        if(lfs_format(&lfs,&cfg) || (err = lfs_mount(&lfs,&cfg)))
        {
            my_printf(&huart1,"LFS:格式化/挂载失败(%d)!\n",err);
            return;
        }
        my_printf(&huart1,"LFS: 格式化&挂载成功.\n");
    }
    else
    {
        my_printf(&huart1,"LFS: 挂载成功.\n");
    }
    
    err = lfs_mkdir(&lfs,"boot");
    if(err && err != LFS_ERR_EXIST)
    {
        my_printf(&huart1,"LFS: 创建'boot'目录失败(%d)!\n",err);
        goto end_test;
    }
    
    if(err == LFS_ERR_OK)
    {
        my_printf(&huart1,"LFS:目录'boot'创建成功.\n");
    }
    
    uint32_t boot_count = 0;
    lfs_file_t file;
    const char*filename = "boot/boot_cnt.txt";
    
    err = lfs_file_open(&lfs,&file, filename, LFS_O_RDWR| LFS_O_CREAT);
    if(err)
    {
        my_printf(&huart1,"LFS:无法打开文件'%s'(%d)!\n",filename,err);
        goto end_test;
    }
    
    lfs_ssize_t r_sz = lfs_file_read(&lfs,&file,&boot_count, sizeof(boot_count));
    if(r_sz < 0)
    {
        my_printf(&huart1,"LFS: 读取文件'%s'失败(%ld), 初始化计数器.\n",filename,(long)r_sz);
        boot_count = 0;
    }
    else if(r_sz != sizeof(boot_count))
    {
        my_printf(&huart1,"LFS: 从'%s'读取%ld字节(预期%d), 初始化计数器.\n",(long)r_sz, filename,(int)sizeof(boot_count));
        boot_count = 0;
    }
    
    boot_count++;
    my_printf(&huart1,"LFS:文件'%s'当前启动计数:%lu\n",filename,boot_count);
    
    err = lfs_file_rewind(&lfs,&file);
    if(err)
    {
        my_printf(&huart1,"LFS: 无法回绕文件'%s'(%d)!\n", filename, err);
        lfs_file_close(&lfs,&file);
        goto end_test;
    }
    
    lfs_ssize_t w_sz = lfs_file_write(&lfs,&file,&boot_count, sizeof(boot_count));
    if(w_sz < 0)
    {
        my_printf(&huart1,"LFS: 写入文件'%s'失败(%ld)!\n",filename,(long)w_sz);
    }
    else if(w_sz != sizeof(boot_count))
    {
        my_printf(&huart1,"LFS: 部分写入'%s'(%ld/%d字节)!\n",filename,(long)w_sz,(int)sizeof(boot_count));
    }
    else
    {
        my_printf(&huart1,"LFS:文件'%s'更新成功.\n",filename);
    }
    
    if(lfs_file_close(&lfs,&file))
    {
        my_printf(&huart1,"LFS: 无法关闭文件'%s'!\n", filename);
    }
    
    my_printf(&huart1,"\r\n[文件系统结构]\r\n");
    my_printf(&huart1,"/(根目录)\r\n");
    list_dir_recursive("/",0);
    
end_test:
    my_printf(&huart1,"--- LittleFS文件系统测试结束---\r\n");
}

void test_spi_flash(void)
{
    uint32_t flash_id;
    uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t test_addr = 0x000000;
    
    my_printf(&huart1,"SPI FLASH测试开始\n");
    spi_flash_init();
    my_printf(&huart1,"SPI Flash初始化完成.\r\n");
    
    flash_id = spi_flash_read_id();
    my_printf(&huart1,"Flash ID:0x%08X\r\n",flash_id);
    
    my_printf(&huart1,"擦除地址0x%08X处的扇区...\r\n", test_addr);
    spi_flash_sector_erase(test_addr);
    my_printf(&huart1,"扇区已擦除.\r\n");
    
    spi_flash_buffer_read(read_buffer,test_addr,SPI_FLASH_PAGE_SIZE);
    int erased_check_ok = 1;
    
    for(int i=0;i<SPI_FLASH_PAGE_SIZE; i++)
    {
        if(read_buffer[i] != 0xFF)
        {
            erased_check_ok = 0;
            break;
        }
    }
    
    if(erased_check_ok)
    {
        my_printf(&huart1,"擦除检查通过. 扇区全部为0xFF.\r\n");
    }
    else
    {
        my_printf(&huart1,"擦除检查失败.\r\n");
    }
    
    const char*message = "Hello from STM32 to SPI FLASH! Microunion Studio Test-12345.";
    uint16_t data_len = strlen(message);
    
    if(data_len >= SPI_FLASH_PAGE_SIZE)
    {
        data_len = SPI_FLASH_PAGE_SIZE - 1;
    }
    
    memset(write_buffer, 0, SPI_FLASH_PAGE_SIZE);
    memcpy(write_buffer, message, data_len);
    write_buffer[data_len] = '\0';
    
    my_printf(&huart1,"向地址0x%08X写入数据:\"%s\"\r\n",test_addr, write_buffer);
    spi_flash_buffer_write(write_buffer,test_addr,SPI_FLASH_PAGE_SIZE);
    my_printf(&huart1,"数据写入完成.\r\n");
    
    my_printf(&huart1,"从地址0x%08X读取数据...\r\n",test_addr);
    memset(read_buffer,0,SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, test_addr, SPI_FLASH_PAGE_SIZE);
    my_printf(&huart1,"读取的数据:\"%s\"\r\n", read_buffer);
    
    if(memcmp(write_buffer, read_buffer,SPI_FLASH_PAGE_SIZE) == 0)
    {
        my_printf(&huart1,"数据验证成功! 写入和读取成功.\r\n");
    }
    else
    {
        my_printf(&huart1,"数据验证失败!\r\n");
    }
    
    my_printf(&huart1,"SPI FLASH测试结束\r\n");
}

void test_sd_fatfs(void)
{
    FRESULT res;
    DIR dir;
    FILINFO fno;
    uint32_t byteswritten;
    uint32_t bytesread;
    char ReadBuffer[256];
    char WriteBuffer[] = "GD32 SD卡FATFS文件系统测试-SD卡读写测试字符串";
    UINT bw,br;
    const char*TestFileName = "SD_TEST.TXT";
    
    my_printf(&huart1,"\n---SD卡FATFS文件系统测试---\r\n");
    
    if(f_mount(&SDFatFS, SDPath, 1) != FR_OK)
    {
        my_printf(&huart1,"SD卡挂载失败!\r\n");
        return;
    }
    my_printf(&huart1,"SD卡挂载成功\r\n");
    
    res = f_mkdir("TEST_DIR");
    if(res == FR_OK)
    {
        my_printf(&huart1,"TEST_DIR目录创建成功\r\n");
    }
    else if(res == FR_EXIST)
    {
        my_printf(&huart1,"TEST_DIR目录已存在\r\n");
    }
    else
    {
        my_printf(&huart1,"目录创建失败，错误码:%d\r\n",res);
    }
    
    my_printf(&huart1,"文件写入测试...\r\n");
    
    res = f_open(&SDFile, TestFileName, FA_CREATE_ALWAYS| FA_WRITE);
    if(res == FR_OK)
    {
        res = f_write(&SDFile, WriteBuffer, strlen(WriteBuffer),&bw);
        if(res == FR_OK && bw == strlen(WriteBuffer))
        {
            my_printf(&huart1,"文件写入字节数:%u字节\r\n",bw);
        }
        else
        {
            my_printf(&huart1,"文件写入失败，错误码:%d\r\n", res);
        }
        
        f_close(&SDFile);
    }
    else
    {
        my_printf(&huart1,"文件打开失败，错误码:%d\r\n",res);
    }
    
    my_printf(&huart1,"文件读取测试...\r\n");
    
    memset(ReadBuffer,0, sizeof(ReadBuffer));
    res = f_open(&SDFile,TestFileName,FA_READ);
    if(res == FR_OK)
    {
        res = f_read(&SDFile, ReadBuffer, sizeof(ReadBuffer)-1,&br);
        if(res == FR_OK)
        {
            ReadBuffer[br] = '\0';
            my_printf(&huart1,"读取字节数:%u字节\r\n", br);
            my_printf(&huart1,"读取内容:%s\r\n", ReadBuffer);
            
            if(strcmp(ReadBuffer, WriteBuffer) == 0)
            {
                my_printf(&huart1,"读写验证:成功\r\n");
            }
            else
            {
                my_printf(&huart1,"读写验证:失败\r\n");
            }
        }
        else
        {
            my_printf(&huart1,"文件读取失败，错误码:%d\r\n", res);
        }
        
        f_close(&SDFile);
    }
    else
    {
        my_printf(&huart1,"文件打开失败，错误码:%d\r\n",res);
    }
    
    my_printf(&huart1,"\r\n文件系统目录结构:\r\n");
    res = f_opendir(&dir,"/");
    if(res == FR_OK)
    {
        for(;;)
        {
            res = f_readdir(&dir,&fno);
            if(res != FR_OK || fno.fname[0] == 0)
                break;
                
            if(fno.fattrib & AM_DIR)
            {
                my_printf(&huart1,"[DIR]%s\r\n",fno.fname);
            }
            else
            {
                my_printf(&huart1,"[FILE]%s(%lu字节)\r\n", fno.fname,(unsigned long)fno.fsize);
            }
        }
        f_closedir(&dir);
    }
    else
    {
        my_printf(&huart1,"打开目录失败，错误码:%d\r\n",res);
    }
    
    f_mount(NULL,SDPath,0);
    my_printf(&huart1,"---SD卡FATFS文件系统测试结束---\r\n");
}