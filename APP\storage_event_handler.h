// 文件名：storage_event_handler.h
// 功能：存储事件处理器头文件，处理存储相关的事件
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __STORAGE_EVENT_HANDLER_H__
#define __STORAGE_EVENT_HANDLER_H__

#include "app_events.h"
#include "app_config.h"

// ========== 存储事件处理器接口 ==========
app_config_status_t storage_event_handler_init(void);                         // 初始化存储事件处理器
app_config_status_t storage_event_handler_deinit(void);                       // 反初始化存储事件处理器

// ========== 事件回调函数 ==========
app_config_status_t storage_handle_sampling_data_event(const app_event_t* event); // 处理采样数据事件
app_config_status_t storage_handle_overlimit_event(const app_event_t* event);     // 处理超限事件

#endif // __STORAGE_EVENT_HANDLER_H__
