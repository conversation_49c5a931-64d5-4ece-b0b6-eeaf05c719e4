// 文件名：app_events.c
// 功能：统一事件管理实现，提供低耦合的模块间通信服务
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#include "app_events.h"
#include "string.h"
#include "main.h"

// ========== 事件管理器状态 ==========
typedef struct {
    app_event_t event_queue[APP_EVENT_QUEUE_SIZE];          // 事件队列
    uint16_t queue_head;                                    // 队列头指针
    uint16_t queue_tail;                                    // 队列尾指针
    uint16_t queue_count;                                   // 队列中事件数量
    app_event_subscription_t subscribers[APP_EVENT_MAX_SUBSCRIBERS]; // 订阅者列表
    uint16_t subscriber_count;                              // 订阅者数量
    uint32_t total_published;                               // 总发布事件数
    uint32_t total_processed;                               // 总处理事件数
    uint32_t total_errors;                                  // 总错误数
    uint8_t initialized;                                    // 初始化标志
} app_event_manager_t;

static app_event_manager_t g_event_manager = {0};

// ========== 内部辅助函数 ==========
static uint8_t is_queue_full(void) // 检查队列是否已满
{
    return g_event_manager.queue_count >= APP_EVENT_QUEUE_SIZE;
}

static uint8_t is_queue_empty(void) // 检查队列是否为空
{
    return g_event_manager.queue_count == 0;
}

static app_config_status_t enqueue_event(const app_event_t* event) // 入队事件
{
    if (!event || is_queue_full()) return APP_CONFIG_ERROR;
    
    g_event_manager.event_queue[g_event_manager.queue_tail] = *event;
    g_event_manager.queue_tail = (g_event_manager.queue_tail + 1) % APP_EVENT_QUEUE_SIZE;
    g_event_manager.queue_count++;
    
    return APP_CONFIG_OK;
}

static app_config_status_t dequeue_event(app_event_t* event) // 出队事件
{
    if (!event || is_queue_empty()) return APP_CONFIG_ERROR;
    
    *event = g_event_manager.event_queue[g_event_manager.queue_head];
    g_event_manager.queue_head = (g_event_manager.queue_head + 1) % APP_EVENT_QUEUE_SIZE;
    g_event_manager.queue_count--;
    
    return APP_CONFIG_OK;
}

static int16_t find_subscriber(app_event_type_t type, app_event_callback_t callback) // 查找订阅者
{
    for (uint16_t i = 0; i < g_event_manager.subscriber_count; i++) {
        if (g_event_manager.subscribers[i].event_type == type && 
            g_event_manager.subscribers[i].callback == callback) {
            return i;
        }
    }
    return -1;
}

// ========== 初始化和管理接口 ==========
app_config_status_t app_events_init(void) // 初始化事件系统
{
    if (g_event_manager.initialized) return APP_CONFIG_OK; // 避免重复初始化

    // 清零所有状态
    memset(&g_event_manager, 0, sizeof(app_event_manager_t));

    g_event_manager.initialized = 1;
    return APP_CONFIG_OK;
}

app_config_status_t app_events_reset(void) // 重置事件系统
{
    APP_RESET_PATTERN("events", g_event_manager.initialized, app_events_init);
}

uint8_t app_events_is_initialized(void) // 检查是否已初始化
{
    return g_event_manager.initialized;
}

// ========== 事件发布接口 ==========
app_config_status_t app_events_publish(app_event_type_t type, 
                                       app_event_priority_t priority,
                                       const app_event_data_t* data) // 发布事件
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (type >= APP_EVENT_COUNT) return APP_CONFIG_INVALID_PARAM;
    
    app_event_t event = {0};
    event.type = type;
    event.priority = priority;
    event.timestamp = HAL_GetTick();
    event.processed = 0;
    
    if (data) {
        event.data = *data;
    }
    
    app_config_status_t status = enqueue_event(&event);
    if (status == APP_CONFIG_OK) {
        g_event_manager.total_published++;
    } else {
        g_event_manager.total_errors++;
    }
    
    return status;
}

app_config_status_t app_events_publish_simple(app_event_type_t type) // 发布简单事件
{
    return app_events_publish(type, APP_EVENT_PRIORITY_NORMAL, NULL);
}

// ========== 事件订阅接口 ==========
app_config_status_t app_events_subscribe(app_event_type_t type, 
                                         app_event_callback_t callback) // 订阅事件
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (type >= APP_EVENT_COUNT || !callback) return APP_CONFIG_INVALID_PARAM;
    if (g_event_manager.subscriber_count >= APP_EVENT_MAX_SUBSCRIBERS) return APP_CONFIG_ERROR;
    
    // 检查是否已经订阅
    if (find_subscriber(type, callback) >= 0) return APP_CONFIG_OK;
    
    // 添加新订阅
    app_event_subscription_t* sub = &g_event_manager.subscribers[g_event_manager.subscriber_count];
    sub->event_type = type;
    sub->callback = callback;
    sub->enabled = 1;
    
    g_event_manager.subscriber_count++;
    return APP_CONFIG_OK;
}

app_config_status_t app_events_unsubscribe(app_event_type_t type, 
                                           app_event_callback_t callback) // 取消订阅
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    int16_t index = find_subscriber(type, callback);
    if (index < 0) return APP_CONFIG_ERROR;
    
    // 移除订阅者(将最后一个移到当前位置)
    if (index < g_event_manager.subscriber_count - 1) {
        g_event_manager.subscribers[index] = 
            g_event_manager.subscribers[g_event_manager.subscriber_count - 1];
    }
    g_event_manager.subscriber_count--;
    
    return APP_CONFIG_OK;
}

app_config_status_t app_events_enable_subscription(app_event_type_t type, 
                                                   app_event_callback_t callback) // 启用订阅
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    int16_t index = find_subscriber(type, callback);
    if (index < 0) return APP_CONFIG_ERROR;
    
    g_event_manager.subscribers[index].enabled = 1;
    return APP_CONFIG_OK;
}

app_config_status_t app_events_disable_subscription(app_event_type_t type, 
                                                    app_event_callback_t callback) // 禁用订阅
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    int16_t index = find_subscriber(type, callback);
    if (index < 0) return APP_CONFIG_ERROR;
    
    g_event_manager.subscribers[index].enabled = 0;
    return APP_CONFIG_OK;
}

// ========== 事件处理接口 ==========
app_config_status_t app_events_process(void) // 处理事件队列
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    while (!is_queue_empty()) {
        app_event_t event;
        if (dequeue_event(&event) != APP_CONFIG_OK) break;
        
        // 通知所有订阅者
        for (uint16_t i = 0; i < g_event_manager.subscriber_count; i++) {
            app_event_subscription_t* sub = &g_event_manager.subscribers[i];
            if (sub->event_type == event.type && sub->enabled && sub->callback) {
                app_config_status_t result = sub->callback(&event);
                if (result != APP_CONFIG_OK) {
                    g_event_manager.total_errors++;
                }
            }
        }
        
        event.processed = 1;
        g_event_manager.total_processed++;
    }
    
    return APP_CONFIG_OK;
}

app_config_status_t app_events_process_priority(app_event_priority_t min_priority) // 处理指定优先级事件
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    // 简化实现：处理所有事件，实际应用中可以优化为优先级队列
    return app_events_process();
}

uint16_t app_events_get_queue_count(void) // 获取队列中事件数量
{
    return g_event_manager.initialized ? g_event_manager.queue_count : 0;
}

uint16_t app_events_get_subscriber_count(app_event_type_t type) // 获取订阅者数量
{
    if (!g_event_manager.initialized || type >= APP_EVENT_COUNT) return 0;
    
    uint16_t count = 0;
    for (uint16_t i = 0; i < g_event_manager.subscriber_count; i++) {
        if (g_event_manager.subscribers[i].event_type == type) {
            count++;
        }
    }
    return count;
}

// ========== 事件查询接口 ==========
const char* app_events_get_type_string(app_event_type_t type) // 获取事件类型字符串
{
    switch (type) {
        case APP_EVENT_NONE:                    return "None";
        case APP_EVENT_SYSTEM_INIT:             return "System Init";
        case APP_EVENT_SYSTEM_ERROR:            return "System Error";
        case APP_EVENT_ADC_CONVERSION_COMPLETE: return "ADC Complete";
        case APP_EVENT_SAMPLING_START:          return "Sampling Start";
        case APP_EVENT_SAMPLING_STOP:           return "Sampling Stop";
        case APP_EVENT_SAMPLING_DATA_READY:     return "Sampling Data";
        case APP_EVENT_OVERLIMIT_DETECTED:      return "Overlimit";
        case APP_EVENT_UART_DATA_RECEIVED:      return "UART Data";
        case APP_EVENT_UART_COMMAND_PARSED:     return "UART Command";
        case APP_EVENT_CONFIG_CHANGED:          return "Config Changed";
        case APP_EVENT_STORAGE_WRITE_COMPLETE:  return "Storage Complete";
        case APP_EVENT_STORAGE_ERROR:           return "Storage Error";
        case APP_EVENT_LED_BLINK_UPDATE:        return "LED Blink";
        case APP_EVENT_BUTTON_PRESSED:          return "Button Press";
        case APP_EVENT_RTC_TIME_UPDATE:         return "RTC Update";
        default:                                return "Unknown";
    }
}

const char* app_events_get_priority_string(app_event_priority_t priority) // 获取优先级字符串
{
    switch (priority) {
        case APP_EVENT_PRIORITY_LOW:      return "Low";
        case APP_EVENT_PRIORITY_NORMAL:   return "Normal";
        case APP_EVENT_PRIORITY_HIGH:     return "High";
        case APP_EVENT_PRIORITY_CRITICAL: return "Critical";
        default:                          return "Unknown";
    }
}

app_config_status_t app_events_get_statistics(uint32_t* total_published,
                                              uint32_t* total_processed,
                                              uint32_t* total_errors) // 获取统计信息
{
    if (!g_event_manager.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    if (total_published) *total_published = g_event_manager.total_published;
    if (total_processed) *total_processed = g_event_manager.total_processed;
    if (total_errors) *total_errors = g_event_manager.total_errors;
    
    return APP_CONFIG_OK;
}

// ========== 便捷事件发布接口实现 ==========
app_config_status_t app_events_publish_adc_complete(float voltage, uint32_t adc_value) // 发布ADC转换完成事件
{
    app_event_data_t data = {0};
    data.adc_data.voltage = voltage;
    data.adc_data.adc_value = adc_value;
    data.adc_data.timestamp = HAL_GetTick();

    return app_events_publish(APP_EVENT_ADC_CONVERSION_COMPLETE, APP_EVENT_PRIORITY_NORMAL, &data);
}

app_config_status_t app_events_publish_overlimit(float voltage) // 发布超限事件
{
    app_event_data_t data = {0};
    data.adc_data.voltage = voltage;
    data.adc_data.is_overlimit = 1;
    data.adc_data.timestamp = HAL_GetTick();

    return app_events_publish(APP_EVENT_OVERLIMIT_DETECTED, APP_EVENT_PRIORITY_HIGH, &data);
}

app_config_status_t app_events_publish_sampling_start(void) // 发布采样开始事件
{
    return app_events_publish_simple(APP_EVENT_SAMPLING_START);
}

app_config_status_t app_events_publish_sampling_stop(void) // 发布采样停止事件
{
    return app_events_publish_simple(APP_EVENT_SAMPLING_STOP);
}

app_config_status_t app_events_publish_sampling_data(float voltage, uint8_t is_overlimit) // 发布采样数据事件
{
    app_event_data_t data = {0};
    data.adc_data.voltage = voltage;
    data.adc_data.is_overlimit = is_overlimit;
    data.adc_data.timestamp = HAL_GetTick();

    return app_events_publish(APP_EVENT_SAMPLING_DATA_READY, APP_EVENT_PRIORITY_NORMAL, &data);
}

app_config_status_t app_events_publish_uart_data(uint8_t* data_ptr, uint16_t length) // 发布串口数据事件
{
    app_event_data_t data = {0};
    data.uart_data.data = data_ptr;
    data.uart_data.length = length;
    data.uart_data.timestamp = HAL_GetTick();

    return app_events_publish(APP_EVENT_UART_DATA_RECEIVED, APP_EVENT_PRIORITY_NORMAL, &data);
}

app_config_status_t app_events_publish_uart_command(void) // 发布串口命令事件
{
    return app_events_publish_simple(APP_EVENT_UART_COMMAND_PARSED);
}

app_config_status_t app_events_publish_system_error(uint8_t error_code, const char* message) // 发布系统错误事件
{
    app_event_data_t data = {0};
    data.error_data.error_code = error_code;
    data.error_data.error_message = message;
    data.error_data.timestamp = HAL_GetTick();

    return app_events_publish(APP_EVENT_SYSTEM_ERROR, APP_EVENT_PRIORITY_CRITICAL, &data);
}

app_config_status_t app_events_publish_config_changed(void) // 发布配置变更事件
{
    return app_events_publish_simple(APP_EVENT_CONFIG_CHANGED);
}

app_config_status_t app_events_publish_storage_complete(void) // 发布存储完成事件
{
    return app_events_publish_simple(APP_EVENT_STORAGE_WRITE_COMPLETE);
}

app_config_status_t app_events_publish_storage_error(uint8_t error_code) // 发布存储错误事件
{
    app_event_data_t data = {0};
    data.error_data.error_code = error_code;
    data.error_data.timestamp = HAL_GetTick();

    return app_events_publish(APP_EVENT_STORAGE_ERROR, APP_EVENT_PRIORITY_HIGH, &data);
}

app_config_status_t app_events_publish_button_press(uint8_t button_id, uint8_t press_type) // 发布按键事件
{
    app_event_data_t data = {0};
    data.button_data.button_id = button_id;
    data.button_data.press_type = press_type;
    data.button_data.timestamp = HAL_GetTick();

    return app_events_publish(APP_EVENT_BUTTON_PRESSED, APP_EVENT_PRIORITY_NORMAL, &data);
}
