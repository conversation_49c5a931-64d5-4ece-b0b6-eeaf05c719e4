/**
 ******************************************************************************
 * @file           : hello_world.c
 * @brief          : Hello World application module
 ******************************************************************************
 * @attention
 *
 * This module provides Hello World functionality for the embedded system
 * including UART output and OLED display capabilities.
 *
 ******************************************************************************
 */

#include "hello_world.h"
#include "mydefine.h"

/* 外部变量声明 */
extern u8g2_t u8g2;

/**
 * @brief  初始化 Hello World 模块
 * @retval None
 */
void hello_world_init(void)
{
    printf("Hello World module initialized!\r\n");
}

/**
 * @brief  通过 UART 串口输出 Hello World
 * @retval None
 */
void hello_world_print_uart(void)
{
    printf("Hello World from UART!\r\n");
    printf("System: STM32F4 Embedded System\r\n");
    printf("Date: %s\r\n", __DATE__);
    printf("Time: %s\r\n", __TIME__);
    printf("============================\r\n");
}

/**
 * @brief  在 OLED 显示屏上显示 Hello World (使用自定义 OLED 库)
 * @retval None
 */
void hello_world_display_oled(void)
{
    // 清屏
    OLED_Clear();
    
    // 显示 Hello World 文本
    OLED_ShowStr(0, 0, "Hello World!", 16);
    OLED_ShowStr(0, 2, "STM32F4 System", 8);
}

/**
 * @brief  在 OLED 显示屏上显示 Hello World (使用 u8g2 库)
 * @retval None
 */
void hello_world_display_u8g2(void)
{
    u8g2_ClearBuffer(&u8g2);
    
    // 设置字体
    u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr);
    
    // 显示文本
    u8g2_DrawStr(&u8g2, 0, 10, "Hello World!");
    u8g2_DrawStr(&u8g2, 0, 25, "STM32F4 System");
    
    // 发送缓冲区到显示屏
    u8g2_SendBuffer(&u8g2);
}

/**
 * @brief  运行所有 Hello World 功能
 * @retval None
 */
void hello_world_run_all(void)
{
    printf("\r\n=== Hello World Demo ===\r\n");
    
    // UART 输出
    hello_world_print_uart();
    
    // OLED 显示 (使用自定义库)
    hello_world_display_oled();
    
    // 延时一下让用户看到效果
    HAL_Delay(2000);
    
    // OLED 显示 (使用 u8g2 库)
    hello_world_display_u8g2();
    
    printf("Hello World demo completed!\r\n");
}
