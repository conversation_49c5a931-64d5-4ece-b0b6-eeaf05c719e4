// 文件名：app_state.h
// 功能：统一全局状态管理头文件，集中管理所有应用层全局状态
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __APP_STATE_H__
#define __APP_STATE_H__

#include "stdint.h"
#include "app_config.h"
#include "usart.h"
#include "u8g2.h"
#include "ringbuffer.h"
#include "ff.h"
#include "rtc.h"
#include "dma.h"

// ========== 系统状态枚举 ==========
typedef enum {
    APP_STATE_UNINITIALIZED = 0,   // 未初始化
    APP_STATE_INITIALIZING = 1,    // 初始化中
    APP_STATE_RUNNING = 2,         // 正常运行
    APP_STATE_ERROR = 3,           // 错误状态
    APP_STATE_SUSPENDED = 4        // 挂起状态
} app_system_state_t;

// ========== UART状态结构 ==========
typedef struct {
    uint16_t rx_index;                          // 接收索引
    uint32_t rx_ticks;                          // 接收时间戳
    uint8_t rx_buffer[APP_UART_BUFFER_SIZE];    // 接收缓冲区
    uint8_t rx_dma_buffer[APP_UART_BUFFER_SIZE]; // DMA接收缓冲区
    uint8_t dma_buffer[APP_UART_BUFFER_SIZE];   // DMA处理缓冲区
    uint8_t send_flag;                          // 发送标志
    struct rt_ringbuffer ringbuffer;           // 环形缓冲区
    uint8_t ringbuffer_pool[APP_UART_BUFFER_SIZE]; // 环形缓冲区内存池
} app_uart_state_t;

// ========== ADC状态结构 ==========
typedef struct {
    uint32_t adc_val;                           // 当前ADC值
    float voltage;                              // 当前电压值
    uint32_t dac_val_buffer[APP_ADC_BUFFER_SIZE / 2];  // DAC数值缓冲区
    uint32_t res_val_buffer[APP_ADC_BUFFER_SIZE / 2];  // 结果数值缓冲区
    uint32_t adc_val_buffer[APP_ADC_BUFFER_SIZE];      // ADC采样缓冲区
    uint8_t conv_end_flag;                      // 转换完成标志
    uint8_t wave_analysis_flag;                 // 波形分析标志
    uint8_t wave_query_type;                    // 波形查询类型
} app_adc_state_t;

// ========== LED状态结构 ==========
typedef struct {
    uint8_t led_states[APP_LED_COUNT];          // LED状态数组
    uint32_t blink_time;                        // 闪烁时间戳
    uint8_t blink_state;                        // 闪烁状态
} app_led_state_t;

// ========== 采样状态结构 ==========
typedef struct {
    uint8_t is_active;                          // 采样是否激活
    uint32_t last_sample_time;                  // 上次采样时间
    uint32_t cycle_seconds;                     // 采样周期(秒)
    uint8_t output_enabled;                     // 输出使能标志
    uint32_t last_output_time;                  // 上次输出时间
    uint8_t output_format;                      // 输出格式(0=normal, 1=hidden)
} app_sampling_state_t;

// ========== 系统硬件状态结构 ==========
typedef struct {
    uint8_t flash_status;                       // Flash状态
    uint8_t sd_card_status;                     // SD卡状态
    uint8_t rtc_status;                         // RTC状态
    uint8_t oled_status;                        // OLED状态
    uint32_t boot_count;                        // 启动次数
} app_hardware_state_t;

// ========== 命令处理状态结构 ==========
typedef struct {
    uint8_t cmd_state;                          // 命令状态(0=idle, 1=wait_ratio, 2=wait_limit, 3=wait_rtc)
    uint8_t interactive_mode;                   // 交互模式标志
} app_command_state_t;

// ========== 全局状态管理结构 ==========
typedef struct {
    app_system_state_t system_state;           // 系统状态
    app_uart_state_t uart_state;               // UART状态
    app_adc_state_t adc_state;                  // ADC状态
    app_led_state_t led_state;                  // LED状态
    app_sampling_state_t sampling_state;       // 采样状态
    app_hardware_state_t hardware_state;       // 硬件状态
    app_command_state_t command_state;         // 命令状态
    uint32_t system_tick;                       // 系统时钟计数
    uint8_t initialized;                        // 初始化标志
} app_global_state_t;

// ========== 状态管理接口声明 ==========
// 初始化和管理
app_config_status_t app_state_init(void);                              // 初始化状态管理系统
app_config_status_t app_state_reset(void);                             // 重置所有状态
app_config_status_t app_state_validate(void);                          // 验证状态一致性
uint8_t app_state_is_initialized(void);                                // 检查是否已初始化

// 系统状态管理
app_system_state_t app_state_get_system_state(void);                   // 获取系统状态
app_config_status_t app_state_set_system_state(app_system_state_t state); // 设置系统状态
const char* app_state_get_system_state_string(app_system_state_t state); // 获取状态字符串

// UART状态管理
app_uart_state_t* app_state_get_uart_state(void);                      // 获取UART状态指针
uint8_t* app_state_get_uart_rx_buffer(void);                           // 获取UART接收缓冲区
uint8_t* app_state_get_uart_dma_buffer(void);                          // 获取UART DMA缓冲区
struct rt_ringbuffer* app_state_get_uart_ringbuffer(void);             // 获取环形缓冲区

// ADC状态管理
app_adc_state_t* app_state_get_adc_state(void);                        // 获取ADC状态指针
float app_state_get_current_voltage(void);                             // 获取当前电压值
app_config_status_t app_state_set_current_voltage(float voltage);      // 设置当前电压值
uint8_t app_state_get_adc_conv_end_flag(void);                         // 获取ADC转换完成标志
app_config_status_t app_state_set_adc_conv_end_flag(uint8_t flag);     // 设置ADC转换完成标志

// LED状态管理
app_led_state_t* app_state_get_led_state(void);                        // 获取LED状态指针
uint8_t* app_state_get_led_array(void);                                // 获取LED状态数组
app_config_status_t app_state_set_led(uint8_t index, uint8_t state);   // 设置单个LED状态
uint8_t app_state_get_led_blink_state(void);                           // 获取LED闪烁状态
app_config_status_t app_state_update_led_blink(void);                  // 更新LED闪烁状态

// 采样状态管理
app_sampling_state_t* app_state_get_sampling_state(void);              // 获取采样状态指针
uint8_t app_state_is_sampling_active(void);                            // 检查采样是否激活
app_config_status_t app_state_set_sampling_active(uint8_t active);     // 设置采样激活状态
uint8_t app_state_get_output_format(void);                             // 获取输出格式
app_config_status_t app_state_set_output_format(uint8_t format);       // 设置输出格式

// 硬件状态管理
app_hardware_state_t* app_state_get_hardware_state(void);              // 获取硬件状态指针
uint8_t app_state_get_flash_status(void);                              // 获取Flash状态
app_config_status_t app_state_set_flash_status(uint8_t status);        // 设置Flash状态
uint32_t app_state_get_boot_count(void);                               // 获取启动次数
app_config_status_t app_state_increment_boot_count(void);              // 增加启动次数

// 命令状态管理
app_command_state_t* app_state_get_command_state(void);                // 获取命令状态指针
uint8_t app_state_get_cmd_state(void);                                 // 获取命令状态
app_config_status_t app_state_set_cmd_state(uint8_t state);            // 设置命令状态

// 外部硬件句柄访问接口
UART_HandleTypeDef* app_state_get_uart_handle(void);                   // 获取UART句柄
DMA_HandleTypeDef* app_state_get_dma_handle(void);                     // 获取DMA句柄
u8g2_t* app_state_get_u8g2_handle(void);                               // 获取U8G2句柄
RTC_HandleTypeDef* app_state_get_rtc_handle(void);                     // 获取RTC句柄
FATFS* app_state_get_fatfs_handle(void);                               // 获取FATFS句柄

#endif // __APP_STATE_H__
