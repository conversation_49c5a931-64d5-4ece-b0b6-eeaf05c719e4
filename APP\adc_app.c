
#include "adc_app.h"
#include "tim.h"
#include "app_config.h" // 使用统一配置管理

#define ADC_MODE (3)

#if ADC_MODE == 1

// ADC状态已迁移到统一状态管理系统

void adc_task(void) // ADC轮询采样任务 参数:无 返回:无
{
    HAL_ADC_Start(&hadc1); // 启动ADC转换

    if (HAL_ADC_PollForConversion(&hadc1, 1000) == HAL_OK) // 等待转换完成(轮询式)，超时1000ms
    {
        uint32_t adc_val = HAL_ADC_GetValue(&hadc1); // 转换成功，获取数字结果(0-4095 for 12-bit)

        float voltage = (float)adc_val * app_config_get_adc_reference_voltage() / APP_ADC_MAX_VALUE; // 使用统一配置的参考电压和分辨率
        app_state_set_current_voltage(voltage); // 保存到统一状态管理

        // 发布ADC转换完成事件
        app_events_publish_adc_complete(voltage, adc_val);

        my_printf(&huart1, "ADC Value: %lu, Voltage: %.2fV\n", adc_val, voltage); // 输出采样结果
    }
    else
    {
        // 转换超时错误处理
        // my_printf(&huart1, "ADC Poll Timeout!\n");
    }

    // 根据CubeMX配置决定是否需要手动停止ADC
    // HAL_ADC_Stop(&hadc1);
}

#elif ADC_MODE == 2 // DMA连续转换模式

#define ADC_DMA_BUFFER_SIZE 32                        // DMA缓冲区大小，可根据需要调整
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE];         // DMA目标缓冲区
__IO uint32_t adc_val;                                // 存储滤波后平均ADC值
__IO float voltage;                                   // 存储计算后的电压值

void adc_dma_init(void) // ADC DMA模式初始化 参数:无 返回:无
{
    // 启动ADC并使用DMA传输
    // hadc1: ADC句柄
    // (uint32_t*)adc_dma_buffer: DMA目标缓冲区地址(HAL库通常需要uint32_t*)
    // ADC_DMA_BUFFER_SIZE: 缓冲区大小，即采样点数量(单位：采样点)

    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_dma_buffer, ADC_DMA_BUFFER_SIZE);
}

void adc_task(void) // DMA模式采样任务处理 参数:无 返回:无
{
    uint32_t adc_sum = 0; // 累加和变量

    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) // 遍历DMA缓冲区中所有采样值进行求和
    {
        adc_sum += adc_dma_buffer[i]; // 注意：这里直接读取缓冲区，可能包含不同时刻的采样值
    }

    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; // 计算平均ADC值

    voltage = ((float)adc_val * app_config_get_adc_reference_voltage()) / APP_ADC_MAX_VALUE; // 使用统一配置的参考电压和分辨率

    my_printf(&huart1, "Average ADC: %lu, Voltage: %.2fV\n", adc_val, voltage); // 使用计算后的平均值
}

#elif ADC_MODE == 3 // DMA定时器触发采样模式

#define BUFFER_SIZE APP_ADC_BUFFER_SIZE // 使用统一配置的缓冲区大小

extern DMA_HandleTypeDef hdma_adc1; // 外部DMA句柄

uint32_t dac_val_buffer[BUFFER_SIZE / 2];  // DAC数值缓冲区
uint32_t res_val_buffer[BUFFER_SIZE / 2];  // 结果数值缓冲区
__IO uint32_t adc_val_buffer[BUFFER_SIZE]; // ADC采样缓冲区
__IO float voltage;                        // 计算后的电压值
__IO uint8_t AdcConvEnd = 0;               // ADC转换完成标志
uint8_t wave_analysis_flag = 0;            // 波形分析标志位
uint8_t wave_query_type = 0;               // 波形查询类型：0=全部, 1=幅值, 2=频率, 3=峰值

void adc_tim_dma_init(void) // ADC定时器DMA模式初始化 参数:无 返回:无
{
    HAL_TIM_Base_Start(&htim3);                                               // 启动定时器3
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);      // 启动ADC DMA采样
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);                             // 禁用半传输中断
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc) // ADC转换完成回调函数 参数:ADC句柄 返回:无
{
    UNUSED(hadc);
    if (hadc == &hadc1) // 确认是hadc1句柄
    {
        HAL_ADC_Stop_DMA(hadc); // 停止DMA，等待处理
        AdcConvEnd = 1;         // 设置标志位
    }
}

void adc_task(void) // 定时器DMA模式采样任务处理 参数:无 返回:无
{
    // 一次采样转换 3(采样) + 12.5(转换) = 15.5 ADC时钟周期
    // 假设ADC时钟14MHz(来自HSI/PLL), 一次转换时间: 15.5 / 14MHz ~= 1.1 us
    // BUFFER_SIZE个转换总时间: 1000 * 1.1 us = 1.1 ms (估算值)
    // 定时器触发频率需要匹配采样率需求

    if (AdcConvEnd) // 检查转换完成标志
    {
        // 示例：将采集到的数据复制到另一个缓冲区(原始未知具体原理逻辑用途)
        for (uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            dac_val_buffer[i] = adc_val_buffer[i * 2 + 1]; // 将ADC数据拆分复制为dac_val_buffer数组等
            res_val_buffer[i] = adc_val_buffer[i * 2];     // 结果缓冲区
        }
        uint32_t res_sum = 0;                              // 累加和
        for (uint16_t i = 0; i < BUFFER_SIZE / 2; i++)     // 将res_val_buffer中的数据转换为电压值
        {
            res_sum += res_val_buffer[i];
        }

        uint32_t res_avg = res_sum / (BUFFER_SIZE / 2);    // 计算平均值
        voltage = (float)res_avg * app_config_get_adc_reference_voltage() / APP_ADC_MAX_VALUE; // 使用统一配置转换为电压值

        // 清空缓冲区数据(可选，根据具体需求逻辑)
        // memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

        AdcConvEnd = 0; // 重置ADC DMA完成标志位，准备下一次采集

        // 重新启动ADC DMA进行下一次采集
        // 注意：这里定时器仍在运行，ADC的具体配置决定是否需要手动停止/启动DMA
        // 需要根据TIM3和ADC的具体配置决定是否需要这些操作
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE); // 重新启动DMA采集
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);                        // 再次禁用半传输中断
    }
}

#endif
