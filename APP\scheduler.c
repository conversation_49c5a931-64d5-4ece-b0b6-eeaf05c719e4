#include "scheduler.h"

// ========== 内部状态管理 ==========
static uint8_t g_legacy_scheduler_initialized = 0;

// ========== 兼容性接口实现 ==========
void scheduler_init(void) // 初始化任务调度器 参数:无 返回:无
{
    if (g_legacy_scheduler_initialized) return;

    // 初始化高级调度器
    scheduler_status_t status = advanced_scheduler_init();
    if (status != SCHEDULER_STATUS_OK) {
        my_printf(&huart1, "Warning: Advanced scheduler init failed: %s\r\n",
                  scheduler_get_status_string(status));
        return;
    }

    // 注册系统任务
    status = scheduler_register_system_tasks();
    if (status != SCHEDULER_STATUS_OK) {
        my_printf(&huart1, "Warning: System tasks registration failed: %s\r\n",
                  scheduler_get_status_string(status));
        return;
    }

    // 启动调度器
    status = advanced_scheduler_start();
    if (status != SCHEDULER_STATUS_OK) {
        my_printf(&huart1, "Warning: Scheduler start failed: %s\r\n",
                  scheduler_get_status_string(status));
        return;
    }

    g_legacy_scheduler_initialized = 1;
    my_printf(&huart1, "Advanced scheduler initialized successfully\r\n");
}

void scheduler_run(void) // 运行任务调度器主循环 参数:无 返回:无
{
    if (!g_legacy_scheduler_initialized) return;

    // 调用高级调度器的运行函数
    scheduler_status_t status = advanced_scheduler_run();
    if (status != SCHEDULER_STATUS_OK && status != SCHEDULER_STATUS_NOT_INITIALIZED) {
        // 只在严重错误时输出警告，避免频繁打印
        static uint32_t last_error_time = 0;
        uint32_t current_time = hal_timer_get_tick();
        if (current_time - last_error_time > 5000) { // 5秒内只打印一次错误
            my_printf(&huart1, "Scheduler run error: %s\r\n",
                      scheduler_get_status_string(status));
            last_error_time = current_time;
        }
    }
}

// ========== 扩展接口实现 ==========
void scheduler_start_all_tasks(void) // 启动所有系统任务
{
    if (!g_legacy_scheduler_initialized) {
        my_printf(&huart1, "Scheduler not initialized\r\n");
        return;
    }

    uint8_t task_count = scheduler_get_task_count();
    my_printf(&huart1, "Starting %d system tasks...\r\n", task_count);

    for (uint8_t i = 0; i < task_count; i++) {
        scheduler_enable_task(i);
    }

    my_printf(&huart1, "All tasks started\r\n");
}

void scheduler_stop_all_tasks(void) // 停止所有系统任务
{
    if (!g_legacy_scheduler_initialized) {
        my_printf(&huart1, "Scheduler not initialized\r\n");
        return;
    }

    uint8_t task_count = scheduler_get_task_count();
    my_printf(&huart1, "Stopping %d system tasks...\r\n", task_count);

    for (uint8_t i = 0; i < task_count; i++) {
        scheduler_disable_task(i);
    }

    my_printf(&huart1, "All tasks stopped\r\n");
}

void scheduler_print_status(void) // 打印调度器状态
{
    if (!g_legacy_scheduler_initialized) {
        my_printf(&huart1, "Scheduler not initialized\r\n");
        return;
    }

    my_printf(&huart1, "\r\n=== Scheduler Status ===\r\n");
    my_printf(&huart1, "Running: %s\r\n", scheduler_is_running() ? "Yes" : "No");
    my_printf(&huart1, "Task Count: %d\r\n", scheduler_get_task_count());
    my_printf(&huart1, "Total Execution Time: %lu ms\r\n", scheduler_get_total_execution_time());
    my_printf(&huart1, "========================\r\n");
}
