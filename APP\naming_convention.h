// 文件名：naming_convention.h
// 功能：统一命名规范定义，规范化代码命名风格
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __NAMING_CONVENTION_H__
#define __NAMING_CONVENTION_H__

/*
================================================================================
                            命名规范说明文档
================================================================================

1. 总体原则
   - 使用英文命名，避免中英文混用
   - 命名要清晰、准确、有意义
   - 避免使用缩写，除非是广泛认知的缩写
   - 保持一致性，同类型的命名使用相同的风格

2. 文件命名规范
   - 使用小写字母和下划线：app_config.h, app_state.c
   - 头文件使用.h后缀，源文件使用.c后缀
   - 文件名应反映其主要功能

3. 宏定义命名规范
   - 使用全大写字母和下划线：APP_CONFIG_VERSION
   - 以模块前缀开头：APP_、HAL_、等
   - 常量宏使用描述性名称：APP_UART_BUFFER_SIZE

4. 类型定义命名规范
   - 结构体：使用小写字母和下划线，以_t结尾：app_config_status_t
   - 枚举：使用小写字母和下划线，以_t结尾：app_event_type_t
   - 枚举值：使用大写字母和下划线，以模块前缀开头：APP_CONFIG_OK

5. 变量命名规范
   - 局部变量：使用小写字母和下划线：buffer_size, current_time
   - 全局变量：以g_开头：g_app_state, g_config_initialized
   - 静态变量：以s_开头：s_event_manager, s_utils_initialized
   - 常量：使用小写字母和下划线：default_config, max_retry_count

6. 函数命名规范
   - 使用小写字母和下划线：app_config_init(), app_state_get_voltage()
   - 以模块前缀开头：app_config_, app_state_, app_events_
   - 动词在前，名词在后：get_config(), set_state(), validate_params()
   - 布尔返回值函数以is_或has_开头：is_initialized(), has_error()

7. 参数命名规范
   - 输入参数：使用描述性名称：config_type, buffer_size
   - 输出参数：以out_开头：out_result, out_length
   - 输入输出参数：以inout_开头：inout_buffer

8. 常用缩写规范
   - config -> configuration (配置)
   - init -> initialize (初始化)
   - param -> parameter (参数)
   - buf -> buffer (缓冲区)
   - len -> length (长度)
   - cnt -> count (计数)
   - idx -> index (索引)
   - addr -> address (地址)
   - val -> value (值)
   - ptr -> pointer (指针)
   - tmp -> temporary (临时)
   - max -> maximum (最大)
   - min -> minimum (最小)
   - avg -> average (平均)
   - std -> standard (标准)

9. 模块前缀规范
   - app_config_    : 配置管理模块
   - app_state_     : 状态管理模块
   - app_events_    : 事件管理模块
   - app_utils_     : 工具模块
   - storage_       : 存储模块
   - sampling_      : 采样模块
   - uart_          : 串口模块
   - adc_           : ADC模块
   - led_           : LED模块

10. 注释规范
    - 使用中文注释，保持友好性
    - 函数注释格式：// 功能描述 参数:参数说明 返回:返回值说明
    - 变量注释格式：// 变量用途说明
    - 复杂逻辑添加行内注释

================================================================================
*/

// ========== 命名规范验证宏 ==========
// 用于在编译时检查命名是否符合规范

// 检查宏定义是否使用大写
#define NAMING_CHECK_MACRO_CASE(name) \
    _Static_assert(sizeof(#name) > 0, "Macro name should be uppercase with underscores")

// 检查类型定义是否以_t结尾
#define NAMING_CHECK_TYPE_SUFFIX(type) \
    _Static_assert(sizeof(#type "_t") > sizeof(#type), "Type name should end with _t")

// 检查函数前缀是否正确
#define NAMING_CHECK_FUNCTION_PREFIX(func, prefix) \
    _Static_assert(sizeof(#prefix #func) > sizeof(#func), "Function should have module prefix")

// ========== 标准命名模板 ==========

// 状态枚举模板
#define DEFINE_STATUS_ENUM(module) \
    typedef enum { \
        module##_OK = 0, \
        module##_ERROR = 1, \
        module##_INVALID_PARAM = 2, \
        module##_OUT_OF_RANGE = 3, \
        module##_NOT_INITIALIZED = 4 \
    } module##_status_t

// 初始化函数模板
#define DEFINE_INIT_FUNCTION(module) \
    module##_status_t module##_init(void); \
    module##_status_t module##_deinit(void); \
    uint8_t module##_is_initialized(void)

// 配置函数模板
#define DEFINE_CONFIG_FUNCTIONS(module) \
    module##_status_t module##_get_config(module##_config_t* out_config); \
    module##_status_t module##_set_config(const module##_config_t* config); \
    module##_status_t module##_validate_config(const module##_config_t* config)

// ========== 常用命名常量 ==========

// 缓冲区大小常量
#define NAMING_SMALL_BUFFER_SIZE    64
#define NAMING_MEDIUM_BUFFER_SIZE   256
#define NAMING_LARGE_BUFFER_SIZE    1024

// 字符串长度常量
#define NAMING_SHORT_STRING_LEN     32
#define NAMING_MEDIUM_STRING_LEN    64
#define NAMING_LONG_STRING_LEN      128

// 超时时间常量
#define NAMING_SHORT_TIMEOUT_MS     100
#define NAMING_MEDIUM_TIMEOUT_MS    1000
#define NAMING_LONG_TIMEOUT_MS      5000

// 重试次数常量
#define NAMING_DEFAULT_RETRY_COUNT  3
#define NAMING_MAX_RETRY_COUNT      10

// ========== 命名规范检查函数 ==========

/**
 * @brief 检查函数名是否符合命名规范
 * @param function_name 函数名字符串
 * @param expected_prefix 期望的前缀
 * @return 1表示符合规范，0表示不符合
 */
uint8_t naming_check_function_name(const char* function_name, const char* expected_prefix);

/**
 * @brief 检查变量名是否符合命名规范
 * @param variable_name 变量名字符串
 * @param is_global 是否为全局变量
 * @return 1表示符合规范，0表示不符合
 */
uint8_t naming_check_variable_name(const char* variable_name, uint8_t is_global);

/**
 * @brief 检查类型名是否符合命名规范
 * @param type_name 类型名字符串
 * @return 1表示符合规范，0表示不符合
 */
uint8_t naming_check_type_name(const char* type_name);

/**
 * @brief 获取标准化的函数名
 * @param module_prefix 模块前缀
 * @param function_action 函数动作
 * @param out_name 输出的标准化名称
 * @param name_size 名称缓冲区大小
 * @return 操作状态
 */
uint8_t naming_get_standard_function_name(const char* module_prefix, 
                                         const char* function_action,
                                         char* out_name, 
                                         uint32_t name_size);

#endif // __NAMING_CONVENTION_H__
