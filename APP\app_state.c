// 文件名：app_state.c
// 功能：统一全局状态管理实现，提供集中的状态管理服务
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#include "app_state.h"
#include "string.h"
#include "main.h"

// ========== 全局状态实例 ==========
static app_global_state_t g_app_state = {0};

// ========== 外部硬件句柄引用 ==========
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern u8g2_t u8g2;
extern RTC_HandleTypeDef hrtc;
extern FATFS SDFatFS;

// ========== 初始化和管理接口 ==========
app_config_status_t app_state_init(void) // 初始化状态管理系统
{
    if (g_app_state.initialized) return APP_CONFIG_OK; // 避免重复初始化
    
    // 清零所有状态
    memset(&g_app_state, 0, sizeof(app_global_state_t));
    
    // 初始化系统状态
    g_app_state.system_state = APP_STATE_INITIALIZING;
    
    // 初始化UART状态
    g_app_state.uart_state.rx_index = 0;
    g_app_state.uart_state.rx_ticks = 0;
    g_app_state.uart_state.send_flag = 0;
    
    // 初始化ADC状态
    g_app_state.adc_state.adc_val = 0;
    g_app_state.adc_state.voltage = 0.0f;
    g_app_state.adc_state.conv_end_flag = 0;
    g_app_state.adc_state.wave_analysis_flag = 0;
    g_app_state.adc_state.wave_query_type = 0;
    
    // 初始化LED状态
    memset(g_app_state.led_state.led_states, 0, APP_LED_COUNT);
    g_app_state.led_state.blink_time = 0;
    g_app_state.led_state.blink_state = 0;
    
    // 初始化采样状态
    g_app_state.sampling_state.is_active = 0;
    g_app_state.sampling_state.last_sample_time = 0;
    g_app_state.sampling_state.cycle_seconds = app_config_get_sampling_default_cycle();
    g_app_state.sampling_state.output_enabled = 0;
    g_app_state.sampling_state.last_output_time = 0;
    g_app_state.sampling_state.output_format = 0; // normal format
    
    // 初始化硬件状态
    g_app_state.hardware_state.flash_status = 0;
    g_app_state.hardware_state.sd_card_status = 0;
    g_app_state.hardware_state.rtc_status = 0;
    g_app_state.hardware_state.oled_status = 0;
    g_app_state.hardware_state.boot_count = 0;
    
    // 初始化命令状态
    g_app_state.command_state.cmd_state = 0; // CMD_STATE_IDLE
    g_app_state.command_state.interactive_mode = 0;
    
    // 设置系统时钟和初始化标志
    g_app_state.system_tick = HAL_GetTick();
    g_app_state.initialized = 1;
    g_app_state.system_state = APP_STATE_RUNNING;
    
    return APP_CONFIG_OK;
}

app_config_status_t app_state_reset(void) // 重置所有状态
{
    g_app_state.initialized = 0;
    return app_state_init();
}

app_config_status_t app_state_validate(void) // 验证状态一致性
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    // 验证系统状态
    if (g_app_state.system_state >= 5) return APP_CONFIG_INVALID_PARAM;
    
    // 验证LED状态
    for (uint8_t i = 0; i < APP_LED_COUNT; i++) {
        if (g_app_state.led_state.led_states[i] > 1) return APP_CONFIG_INVALID_PARAM;
    }
    
    // 验证采样状态
    if (g_app_state.sampling_state.output_format > 1) return APP_CONFIG_INVALID_PARAM;
    if (g_app_state.sampling_state.cycle_seconds < APP_SAMPLING_MIN_CYCLE_S || 
        g_app_state.sampling_state.cycle_seconds > APP_SAMPLING_MAX_CYCLE_S) {
        return APP_CONFIG_OUT_OF_RANGE;
    }
    
    return APP_CONFIG_OK;
}

uint8_t app_state_is_initialized(void) // 检查是否已初始化
{
    return g_app_state.initialized;
}

// ========== 系统状态管理 ==========
app_system_state_t app_state_get_system_state(void) // 获取系统状态
{
    return g_app_state.system_state;
}

app_config_status_t app_state_set_system_state(app_system_state_t state) // 设置系统状态
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (state >= 5) return APP_CONFIG_INVALID_PARAM;
    
    g_app_state.system_state = state;
    return APP_CONFIG_OK;
}

const char* app_state_get_system_state_string(app_system_state_t state) // 获取状态字符串
{
    switch (state) {
        case APP_STATE_UNINITIALIZED: return "Uninitialized";
        case APP_STATE_INITIALIZING:  return "Initializing";
        case APP_STATE_RUNNING:       return "Running";
        case APP_STATE_ERROR:         return "Error";
        case APP_STATE_SUSPENDED:     return "Suspended";
        default:                      return "Unknown";
    }
}

// ========== UART状态管理 ==========
app_uart_state_t* app_state_get_uart_state(void) // 获取UART状态指针
{
    return g_app_state.initialized ? &g_app_state.uart_state : NULL;
}

uint8_t* app_state_get_uart_rx_buffer(void) // 获取UART接收缓冲区
{
    return g_app_state.initialized ? g_app_state.uart_state.rx_buffer : NULL;
}

uint8_t* app_state_get_uart_dma_buffer(void) // 获取UART DMA缓冲区
{
    return g_app_state.initialized ? g_app_state.uart_state.dma_buffer : NULL;
}

struct rt_ringbuffer* app_state_get_uart_ringbuffer(void) // 获取环形缓冲区
{
    return g_app_state.initialized ? &g_app_state.uart_state.ringbuffer : NULL;
}

// ========== ADC状态管理 ==========
app_adc_state_t* app_state_get_adc_state(void) // 获取ADC状态指针
{
    return g_app_state.initialized ? &g_app_state.adc_state : NULL;
}

float app_state_get_current_voltage(void) // 获取当前电压值
{
    return g_app_state.initialized ? g_app_state.adc_state.voltage : 0.0f;
}

app_config_status_t app_state_set_current_voltage(float voltage) // 设置当前电压值
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (!app_config_validate_voltage(voltage)) return APP_CONFIG_OUT_OF_RANGE;
    
    g_app_state.adc_state.voltage = voltage;
    return APP_CONFIG_OK;
}

uint8_t app_state_get_adc_conv_end_flag(void) // 获取ADC转换完成标志
{
    return g_app_state.initialized ? g_app_state.adc_state.conv_end_flag : 0;
}

app_config_status_t app_state_set_adc_conv_end_flag(uint8_t flag) // 设置ADC转换完成标志
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    g_app_state.adc_state.conv_end_flag = flag ? 1 : 0;
    return APP_CONFIG_OK;
}

// ========== LED状态管理 ==========
app_led_state_t* app_state_get_led_state(void) // 获取LED状态指针
{
    return g_app_state.initialized ? &g_app_state.led_state : NULL;
}

uint8_t* app_state_get_led_array(void) // 获取LED状态数组
{
    return g_app_state.initialized ? g_app_state.led_state.led_states : NULL;
}

app_config_status_t app_state_set_led(uint8_t index, uint8_t state) // 设置单个LED状态
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (index >= APP_LED_COUNT) return APP_CONFIG_INVALID_PARAM;
    if (state > 1) return APP_CONFIG_INVALID_PARAM;
    
    g_app_state.led_state.led_states[index] = state;
    return APP_CONFIG_OK;
}

uint8_t app_state_get_led_blink_state(void) // 获取LED闪烁状态
{
    return g_app_state.initialized ? g_app_state.led_state.blink_state : 0;
}

app_config_status_t app_state_update_led_blink(void) // 更新LED闪烁状态
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    uint32_t current_time = HAL_GetTick();
    uint32_t blink_period = app_config_get_led_blink_period();
    
    if (current_time - g_app_state.led_state.blink_time >= blink_period) {
        g_app_state.led_state.blink_state ^= 1; // 翻转状态
        g_app_state.led_state.blink_time = current_time;
    }
    
    return APP_CONFIG_OK;
}

// ========== 采样状态管理 ==========
app_sampling_state_t* app_state_get_sampling_state(void) // 获取采样状态指针
{
    return g_app_state.initialized ? &g_app_state.sampling_state : NULL;
}

uint8_t app_state_is_sampling_active(void) // 检查采样是否激活
{
    return g_app_state.initialized ? g_app_state.sampling_state.is_active : 0;
}

app_config_status_t app_state_set_sampling_active(uint8_t active) // 设置采样激活状态
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    
    g_app_state.sampling_state.is_active = active ? 1 : 0;
    if (active) {
        g_app_state.sampling_state.last_sample_time = HAL_GetTick();
    }
    
    return APP_CONFIG_OK;
}

uint8_t app_state_get_output_format(void) // 获取输出格式
{
    return g_app_state.initialized ? g_app_state.sampling_state.output_format : 0;
}

app_config_status_t app_state_set_output_format(uint8_t format) // 设置输出格式
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (format > 1) return APP_CONFIG_INVALID_PARAM;
    
    g_app_state.sampling_state.output_format = format;
    return APP_CONFIG_OK;
}

// ========== 外部硬件句柄访问接口 ==========
UART_HandleTypeDef* app_state_get_uart_handle(void) // 获取UART句柄
{
    return &huart1;
}

DMA_HandleTypeDef* app_state_get_dma_handle(void) // 获取DMA句柄
{
    return &hdma_usart1_rx;
}

u8g2_t* app_state_get_u8g2_handle(void) // 获取U8G2句柄
{
    return &u8g2;
}

RTC_HandleTypeDef* app_state_get_rtc_handle(void) // 获取RTC句柄
{
    return &hrtc;
}

FATFS* app_state_get_fatfs_handle(void) // 获取FATFS句柄
{
    return &SDFatFS;
}

// ========== 硬件状态管理 ==========
app_hardware_state_t* app_state_get_hardware_state(void) // 获取硬件状态指针
{
    return g_app_state.initialized ? &g_app_state.hardware_state : NULL;
}

uint8_t app_state_get_flash_status(void) // 获取Flash状态
{
    return g_app_state.initialized ? g_app_state.hardware_state.flash_status : 0;
}

app_config_status_t app_state_set_flash_status(uint8_t status) // 设置Flash状态
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;

    g_app_state.hardware_state.flash_status = status;
    return APP_CONFIG_OK;
}

uint32_t app_state_get_boot_count(void) // 获取启动次数
{
    return g_app_state.initialized ? g_app_state.hardware_state.boot_count : 0;
}

app_config_status_t app_state_increment_boot_count(void) // 增加启动次数
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;

    if (g_app_state.hardware_state.boot_count < APP_SYSTEM_MAX_BOOT_COUNT) {
        g_app_state.hardware_state.boot_count++;
    }

    return APP_CONFIG_OK;
}

// ========== 命令状态管理 ==========
app_command_state_t* app_state_get_command_state(void) // 获取命令状态指针
{
    return g_app_state.initialized ? &g_app_state.command_state : NULL;
}

uint8_t app_state_get_cmd_state(void) // 获取命令状态
{
    return g_app_state.initialized ? g_app_state.command_state.cmd_state : 0;
}

app_config_status_t app_state_set_cmd_state(uint8_t state) // 设置命令状态
{
    if (!g_app_state.initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (state > 3) return APP_CONFIG_INVALID_PARAM; // 0-3: idle, wait_ratio, wait_limit, wait_rtc

    g_app_state.command_state.cmd_state = state;
    return APP_CONFIG_OK;
}
