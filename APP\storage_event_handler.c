// 文件名：storage_event_handler.c
// 功能：存储事件处理器实现，处理存储相关的事件
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#include "storage_event_handler.h"
#include "data_storage.h"
#include "config_manager.h"
#include "app_state.h"

// ========== 存储事件处理器接口 ==========
app_config_status_t storage_event_handler_init(void) // 初始化存储事件处理器
{
    app_config_status_t status;
    
    // 订阅采样数据事件
    status = app_events_subscribe(APP_EVENT_SAMPLING_DATA_READY, storage_handle_sampling_data_event);
    if (status != APP_CONFIG_OK) return status;
    
    // 订阅超限事件
    status = app_events_subscribe(APP_EVENT_OVERLIMIT_DETECTED, storage_handle_overlimit_event);
    if (status != APP_CONFIG_OK) return status;
    
    return APP_CONFIG_OK;
}

app_config_status_t storage_event_handler_deinit(void) // 反初始化存储事件处理器
{
    // 取消订阅采样数据事件
    app_events_unsubscribe(APP_EVENT_SAMPLING_DATA_READY, storage_handle_sampling_data_event);
    
    // 取消订阅超限事件
    app_events_unsubscribe(APP_EVENT_OVERLIMIT_DETECTED, storage_handle_overlimit_event);
    
    return APP_CONFIG_OK;
}

// ========== 事件回调函数 ==========
app_config_status_t storage_handle_sampling_data_event(const app_event_t* event) // 处理采样数据事件
{
    APP_VALIDATE_POINTER(event);
    APP_HANDLE_ERROR(event->type != APP_EVENT_SAMPLING_DATA_READY, APP_CONFIG_INVALID_PARAM, /* no action */);

    float voltage = event->data.adc_data.voltage;
    uint8_t is_overlimit = event->data.adc_data.is_overlimit;

    // 获取当前输出格式
    uint8_t output_format = app_state_get_output_format();

    // 根据输出格式选择存储方式
    data_storage_status_t result;
    if (output_format == 1) { // OUTPUT_FORMAT_HIDDEN = 1
        result = data_storage_write_hidedata(voltage, is_overlimit);
    } else {
        result = data_storage_write_sample(voltage);
    }

    if (result == DATA_STORAGE_OK) {
        app_events_publish_storage_complete();
        return APP_CONFIG_OK;
    } else {
        app_events_publish_storage_error((uint8_t)result);
        return APP_CONFIG_ERROR;
    }
}

app_config_status_t storage_handle_overlimit_event(const app_event_t* event) // 处理超限事件
{
    APP_VALIDATE_POINTER(event);
    APP_HANDLE_ERROR(event->type != APP_EVENT_OVERLIMIT_DETECTED, APP_CONFIG_INVALID_PARAM, /* no action */);

    float voltage = event->data.adc_data.voltage;

    // 使用工具函数获取限值配置
    float limit_value;
    app_utils_get_limit_value(&limit_value);

    // 存储超限数据到overLimit文件
    data_storage_status_t result = data_storage_write_overlimit(voltage, limit_value);

    if (result == DATA_STORAGE_OK) {
        app_events_publish_storage_complete();
        return APP_CONFIG_OK;
    } else {
        app_events_publish_storage_error((uint8_t)result);
        return APP_CONFIG_ERROR;
    }
}
