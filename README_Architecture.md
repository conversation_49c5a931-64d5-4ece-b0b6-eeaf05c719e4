# STM32F4 嵌入式系统架构文档

## 概述

本文档描述了经过重构优化后的STM32F4嵌入式系统架构，采用了低耦合、高内聚的设计理念，建立了完整的分层架构体系。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 采样控制模块 │ │ 数据存储模块 │ │ 串口命令模块 │ │ 系统检查 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ LED控制模块 │ │ 按键处理模块 │ │ OLED显示模块 │ │ RTC管理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   服务层 (Service Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 统一配置管理 │ │ 统一状态管理 │ │ 统一事件管理 │ │ 公共工具 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 高级调度器   │ │ 命名规范管理 │ │ 硬件抽象层   │ │ 错误处理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   硬件层 (Hardware Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ GPIO/LED    │ │ ADC采样     │ │ UART通信    │ │ RTC时钟 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ SPI Flash   │ │ SD卡存储    │ │ OLED显示    │ │ 定时器   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. 统一配置管理系统 (app_config)

**功能**：集中管理所有应用层配置参数，避免重复定义

**主要特性**：
- 统一的配置常量定义
- 配置参数验证机制
- 默认值管理
- 配置状态枚举

**核心文件**：
- `app_config.h` - 配置常量和接口定义
- `app_config.c` - 配置管理实现

**使用示例**：
```c
// 获取ADC参考电压
float ref_voltage = app_config_get_adc_reference_voltage();

// 验证配置参数
app_config_status_t status = app_config_validate_all();
```

### 2. 统一状态管理系统 (app_state)

**功能**：集中管理全局状态，减少模块间直接变量访问

**主要特性**：
- 全局状态结构体
- 状态访问接口
- 状态验证机制
- 硬件句柄管理

**核心文件**：
- `app_state.h` - 状态结构和接口定义
- `app_state.c` - 状态管理实现

**使用示例**：
```c
// 设置LED状态
app_state_set_led(0, 1);

// 获取当前电压
float voltage = app_state_get_current_voltage();
```

### 3. 统一事件管理系统 (app_events)

**功能**：采用事件驱动机制，降低模块间直接依赖

**主要特性**：
- 事件队列管理
- 事件优先级处理
- 事件订阅机制
- 事件统计功能

**核心文件**：
- `app_events.h` - 事件类型和接口定义
- `app_events.c` - 事件管理实现

**使用示例**：
```c
// 发布ADC完成事件
app_events_publish_adc_complete(voltage, adc_value);

// 处理事件队列
app_events_process();
```

### 4. 公共工具模块 (app_utils)

**功能**：提供通用工具函数，消除代码冗余

**主要特性**：
- 通用初始化模式宏
- 参数验证工具
- 字符串处理工具
- 时间戳管理
- 状态机工具

**核心文件**：
- `app_utils.h` - 工具函数和宏定义
- `app_utils.c` - 工具函数实现

**使用示例**：
```c
// 使用初始化模式宏
APP_INIT_PATTERN("module", g_initialized, init_function);

// 参数验证
APP_VALIDATE_POINTER(ptr);
```

### 5. 命名规范管理 (naming_convention)

**功能**：统一代码命名风格，提高可读性

**主要特性**：
- 命名规范文档
- 命名检查函数
- 标准命名模板
- 常用缩写规范

**核心文件**：
- `naming_convention.h` - 命名规范定义和工具

### 6. 硬件抽象层 (hardware_abstraction)

**功能**：提供统一的硬件访问接口，提高可移植性

**主要特性**：
- GPIO抽象接口
- ADC抽象接口
- UART抽象接口
- RTC抽象接口
- 定时器抽象接口

**核心文件**：
- `hardware_abstraction.h` - 硬件抽象接口定义
- `hardware_abstraction.c` - 硬件抽象实现

**使用示例**：
```c
// 设置LED状态
hal_gpio_set_led(HAL_LED_0, HAL_GPIO_PIN_SET);

// 获取ADC结果
hal_adc_result_t result;
hal_adc_get_result(&result);
```

### 7. 高级任务调度器 (advanced_scheduler)

**功能**：提供灵活的任务管理和调度机制

**主要特性**：
- 动态任务管理
- 优先级调度
- 任务状态管理
- 任务统计功能
- 错误处理机制

**核心文件**：
- `advanced_scheduler.h` - 调度器接口定义
- `advanced_scheduler.c` - 调度器实现
- `scheduler.h/c` - 兼容性适配器

**使用示例**：
```c
// 添加任务
uint8_t task_id = scheduler_add_task("led_task", led_task, 
                                    TASK_PRIORITY_LOW, 
                                    TASK_TYPE_PERIODIC, 1);

// 启用任务
scheduler_enable_task(task_id);
```

## 设计原则

### 1. 低耦合设计
- 模块间通过接口通信，避免直接访问内部变量
- 使用事件机制减少模块间直接依赖
- 硬件抽象层隔离硬件相关代码

### 2. 高内聚设计
- 相关功能集中在同一模块内
- 统一的配置、状态、事件管理
- 公共功能提取到工具模块

### 3. 可扩展性
- 模块化设计便于功能扩展
- 统一的接口规范
- 配置化的参数管理

### 4. 可维护性
- 统一的命名规范
- 完善的错误处理
- 详细的文档说明

## 编译和使用

### 编译要求
- STM32CubeMX生成的HAL库
- 支持C99标准的编译器
- 足够的Flash和RAM空间

### 初始化顺序
```c
// 1. 初始化硬件抽象层
hal_init_all();

// 2. 初始化公共工具
app_utils_init();

// 3. 初始化配置管理
app_config_init();

// 4. 初始化状态管理
app_state_init();

// 5. 初始化事件管理
app_events_init();

// 6. 初始化调度器
scheduler_init();
```

### 主循环
```c
while (1) {
    // 运行任务调度器
    scheduler_run();
    
    // 处理事件队列
    app_events_process();
}
```

## 性能特性

### 内存使用
- 优化的数据结构设计
- 避免内存碎片
- 合理的缓冲区大小

### 实时性
- 优先级调度机制
- 高效的事件处理
- 最小化中断延迟

### 可靠性
- 完善的错误处理
- 参数验证机制
- 硬件状态监控

## 扩展指南

### 添加新模块
1. 遵循命名规范
2. 使用统一的配置管理
3. 集成到事件系统
4. 注册到调度器

### 移植到其他平台
1. 修改硬件抽象层实现
2. 调整配置参数
3. 适配编译环境
4. 测试验证功能

## 故障排除

### 常见问题
1. **编译错误**：检查头文件包含顺序
2. **运行异常**：验证初始化顺序
3. **性能问题**：检查任务优先级设置
4. **内存不足**：优化缓冲区大小配置

### 调试工具
- 串口输出调试信息
- LED状态指示
- 任务执行统计
- 事件队列监控
