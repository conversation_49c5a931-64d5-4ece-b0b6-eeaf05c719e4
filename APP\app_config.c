// 文件名：app_config.c
// 功能：统一应用配置管理实现，提供集中的配置管理服务
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#include "app_config.h"
#include "string.h"

// ========== 内部状态管理 ==========
static uint8_t g_config_initialized = 0;           // 配置系统初始化标志
static uint8_t g_config_version = APP_CONFIG_VERSION; // 当前配置版本

// ========== 配置验证函数 ==========
static app_config_status_t validate_adc_config(void) // 验证ADC配置参数
{
    if (!APP_CONFIG_VALIDATE_BUFFER_SIZE(APP_ADC_BUFFER_SIZE)) return APP_CONFIG_OUT_OF_RANGE;
    if (APP_ADC_REFERENCE_VOLTAGE <= 0.0f || APP_ADC_REFERENCE_VOLTAGE > 5.0f) return APP_CONFIG_OUT_OF_RANGE;
    if (APP_ADC_RESOLUTION_BITS < 8 || APP_ADC_RESOLUTION_BITS > 16) return APP_CONFIG_OUT_OF_RANGE;
    return APP_CONFIG_OK;
}

static app_config_status_t validate_led_config(void) // 验证LED配置参数
{
    if (APP_LED_COUNT == 0 || APP_LED_COUNT > 16) return APP_CONFIG_OUT_OF_RANGE;
    if (!APP_CONFIG_VALIDATE_TIMEOUT(APP_LED_BLINK_PERIOD_MS)) return APP_CONFIG_OUT_OF_RANGE;
    return APP_CONFIG_OK;
}

static app_config_status_t validate_uart_config(void) // 验证串口配置参数
{
    if (!APP_CONFIG_VALIDATE_BUFFER_SIZE(APP_UART_BUFFER_SIZE)) return APP_CONFIG_OUT_OF_RANGE;
    if (APP_UART_BAUDRATE < 9600 || APP_UART_BAUDRATE > 921600) return APP_CONFIG_OUT_OF_RANGE;
    if (!APP_CONFIG_VALIDATE_TIMEOUT(APP_UART_TIMEOUT_MS)) return APP_CONFIG_OUT_OF_RANGE;
    return APP_CONFIG_OK;
}

static app_config_status_t validate_sampling_config(void) // 验证采样配置参数
{
    if (APP_SAMPLING_MIN_CYCLE_S <= 0 || APP_SAMPLING_MIN_CYCLE_S > APP_SAMPLING_MAX_CYCLE_S) return APP_CONFIG_OUT_OF_RANGE;
    if (APP_SAMPLING_MAX_CYCLE_S > 60) return APP_CONFIG_OUT_OF_RANGE;
    if (!APP_CONFIG_VALIDATE_RANGE(APP_SAMPLING_DEFAULT_CYCLE, APP_SAMPLING_MIN_CYCLE_S, APP_SAMPLING_MAX_CYCLE_S)) return APP_CONFIG_OUT_OF_RANGE;
    return APP_CONFIG_OK;
}

static app_config_status_t validate_storage_config(void) // 验证存储配置参数
{
    if (APP_STORAGE_MAX_RECORDS == 0 || APP_STORAGE_MAX_RECORDS > 100) return APP_CONFIG_OUT_OF_RANGE;
    if (APP_STORAGE_FILENAME_LEN < 16 || APP_STORAGE_FILENAME_LEN > 128) return APP_CONFIG_OUT_OF_RANGE;
    if (!APP_CONFIG_VALIDATE_BUFFER_SIZE(APP_STORAGE_BUFFER_SIZE)) return APP_CONFIG_OUT_OF_RANGE;
    return APP_CONFIG_OK;
}

static app_config_status_t validate_parameter_ranges(void) // 验证参数范围配置
{
    if (!APP_CONFIG_VALIDATE_RANGE(APP_RATIO_DEFAULT, APP_RATIO_MIN, APP_RATIO_MAX)) return APP_CONFIG_OUT_OF_RANGE;
    if (!APP_CONFIG_VALIDATE_RANGE(APP_LIMIT_DEFAULT, APP_LIMIT_MIN, APP_LIMIT_MAX)) return APP_CONFIG_OUT_OF_RANGE;
    if (APP_VOLTAGE_MIN >= APP_VOLTAGE_MAX) return APP_CONFIG_OUT_OF_RANGE;
    return APP_CONFIG_OK;
}

// ========== 配置管理接口实现 ==========
app_config_status_t app_config_init(void) // 初始化配置系统
{
    APP_INIT_PATTERN("config", g_config_initialized, app_config_validate_all);
}

app_config_status_t app_config_validate_all(void) // 验证所有配置参数
{
    app_config_status_t status;

    APP_HANDLE_STATUS(status, validate_adc_config(), /* no action */);
    APP_HANDLE_STATUS(status, validate_led_config(), /* no action */);
    APP_HANDLE_STATUS(status, validate_uart_config(), /* no action */);
    APP_HANDLE_STATUS(status, validate_sampling_config(), /* no action */);
    APP_HANDLE_STATUS(status, validate_storage_config(), /* no action */);
    APP_HANDLE_STATUS(status, validate_parameter_ranges(), /* no action */);

    return APP_CONFIG_OK;
}

app_config_status_t app_config_reset_to_default(app_config_type_t type) // 重置指定类型配置为默认值
{
    if (!g_config_initialized) return APP_CONFIG_NOT_INITIALIZED;
    if (type >= APP_CONFIG_TYPE_COUNT) return APP_CONFIG_INVALID_PARAM;
    
    // 注意：这里只是验证配置，实际的重置操作由各模块自己实现
    // 这样保持了低耦合的设计原则
    switch (type) {
        case APP_CONFIG_TYPE_SYSTEM:
        case APP_CONFIG_TYPE_HARDWARE:
        case APP_CONFIG_TYPE_SAMPLING:
        case APP_CONFIG_TYPE_STORAGE:
            return APP_CONFIG_OK; // 配置类型有效
        default:
            return APP_CONFIG_INVALID_PARAM;
    }
}

app_config_status_t app_config_get_version(uint8_t *version) // 获取配置版本号
{
    APP_VALIDATE_POINTER(version);
    APP_CHECK_INIT_PATTERN(g_config_initialized);

    *version = g_config_version;
    return APP_CONFIG_OK;
}

const char* app_config_get_status_string(app_config_status_t status) // 获取状态描述字符串
{
    switch (status) {
        case APP_CONFIG_OK:              return "Config OK";
        case APP_CONFIG_ERROR:           return "Config Error";
        case APP_CONFIG_INVALID_PARAM:   return "Invalid Parameter";
        case APP_CONFIG_OUT_OF_RANGE:    return "Out of Range";
        case APP_CONFIG_NOT_INITIALIZED: return "Not Initialized";
        default:                         return "Unknown Status";
    }
}

// ========== 配置查询接口 ==========
uint8_t app_config_is_initialized(void) // 检查配置系统是否已初始化
{
    return g_config_initialized;
}

uint32_t app_config_get_adc_buffer_size(void) // 获取ADC缓冲区大小
{
    return APP_ADC_BUFFER_SIZE;
}

float app_config_get_adc_reference_voltage(void) // 获取ADC参考电压
{
    return APP_ADC_REFERENCE_VOLTAGE;
}

uint32_t app_config_get_led_blink_period(void) // 获取LED闪烁周期
{
    return APP_LED_BLINK_PERIOD_MS;
}

uint32_t app_config_get_uart_buffer_size(void) // 获取串口缓冲区大小
{
    return APP_UART_BUFFER_SIZE;
}

uint32_t app_config_get_sampling_default_cycle(void) // 获取默认采样周期
{
    return APP_SAMPLING_DEFAULT_CYCLE;
}

uint32_t app_config_get_storage_max_records(void) // 获取存储最大记录数
{
    return APP_STORAGE_MAX_RECORDS;
}

float app_config_get_ratio_default(void) // 获取默认比率值
{
    return APP_RATIO_DEFAULT;
}

float app_config_get_limit_default(void) // 获取默认限值
{
    return APP_LIMIT_DEFAULT;
}

// ========== 配置范围验证接口 ==========
uint8_t app_config_validate_ratio(float ratio) // 验证比率参数
{
    return APP_CONFIG_VALIDATE_RANGE(ratio, APP_RATIO_MIN, APP_RATIO_MAX);
}

uint8_t app_config_validate_limit(float limit) // 验证限值参数
{
    return APP_CONFIG_VALIDATE_RANGE(limit, APP_LIMIT_MIN, APP_LIMIT_MAX);
}

uint8_t app_config_validate_voltage(float voltage) // 验证电压参数
{
    return APP_CONFIG_VALIDATE_RANGE(voltage, APP_VOLTAGE_MIN, APP_VOLTAGE_MAX);
}

uint8_t app_config_validate_sampling_cycle(uint32_t cycle) // 验证采样周期
{
    return APP_CONFIG_VALIDATE_RANGE(cycle, APP_SAMPLING_MIN_CYCLE_S, APP_SAMPLING_MAX_CYCLE_S);
}
