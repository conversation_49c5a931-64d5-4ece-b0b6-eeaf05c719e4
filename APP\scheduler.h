// 文件名：scheduler.h
// 功能：任务调度器头文件，提供系统任务调度管理接口(兼容性适配器)
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef SCHEDULER_H
#define SCHEDULER_H

#include "mydefine.h" // 全局定义头文件
#include "advanced_scheduler.h" // 高级调度器

// ========== 兼容性接口 ==========
void scheduler_init(void); // 初始化任务调度器 参数:无 返回:无
void scheduler_run(void);  // 运行任务调度器主循环 参数:无 返回:无

// ========== 扩展接口 ==========
void scheduler_start_all_tasks(void);  // 启动所有系统任务
void scheduler_stop_all_tasks(void);   // 停止所有系统任务
void scheduler_print_status(void);     // 打印调度器状态

#endif
