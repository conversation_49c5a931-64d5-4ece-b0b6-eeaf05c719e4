#include "led_app.h"
#include "app_config.h" // 使用统一配置管理
#include "hardware_abstraction.h" // 使用硬件抽象层

// LED状态已迁移到统一状态管理系统

void led_disp(uint8_t *ucLed)
{
    static uint8_t previous_led_state = 0xFF; // 记录上次LED状态，避免重复设置
    uint8_t current_led_state = 0x00;

    // 计算当前LED状态
    for (int i = 0; i < APP_LED_COUNT; i++) // 使用配置常量
    {
        if (ucLed[i])
            current_led_state |= (1 << i);
    }

    // 只有状态改变时才更新LED
    if (previous_led_state != current_led_state)
    {
        // 使用硬件抽象层设置LED状态
        for (int i = 0; i < APP_LED_COUNT; i++)
        {
            hal_gpio_pin_state_t led_state = (current_led_state & (1 << i)) ? HAL_GPIO_PIN_SET : HAL_GPIO_PIN_RESET;
            hal_gpio_set_led((hal_led_index_t)i, led_state);
        }

        previous_led_state = current_led_state;
    }
}

void led_task(void)
{
    static uint32_t led1_blink_time = 0; // LED1��˸ʱ���
    static uint8_t led1_blink_state = 0; // LED1��˸״̬

    // LED1����״ָ̬ʾ�߼�
    if (sampling_get_state() == SAMPLING_ACTIVE)
    {
        // 采样状态下使用配置的闪烁周期
        uint32_t current_time = HAL_GetTick();
        uint32_t blink_period = app_config_get_led_blink_period(); // 使用统一配置
        if (current_time - led1_blink_time >= blink_period)
        {
            led1_blink_state ^= 1; // 翻转LED状态
            led1_blink_time = current_time;
        }
        app_state_set_led(0, led1_blink_state); // 使用统一状态管理
    }
    else
    {
        // ֹͣ״̬������
        app_state_set_led(0, 0); // 使用统一状态管理
        led1_blink_state = 0;
    }

    // LED2����״ָ̬ʾ�߼�
    if (sampling_check_overlimit())
    {
        // ����ʱ������LED2
        app_state_set_led(1, 1); // 使用统一状态管理
    }
    else
    {
        // ����ʱ��Ϩ��LED2
        app_state_set_led(1, 0); // 使用统一状态管理
    }

    // 更新LED显示
    uint8_t* ucLed = app_state_get_led_array(); // 获取LED状态数组
    if (ucLed) {
        led_disp(ucLed);
    }
}
