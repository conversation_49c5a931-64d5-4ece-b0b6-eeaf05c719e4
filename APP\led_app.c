#include "led_app.h"
#include "gpio.h"
#include "app_config.h" // 使用统一配置管理

// LED状态已迁移到统一状态管理系统

void led_disp(uint8_t *ucLed)
{
    uint8_t temp = 0x00;
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < APP_LED_COUNT; i++) // 使用配置常量
    {
        if (ucLed[i])
            temp |= (1 << i);
    }

    if (temp_old != temp)
    {

        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 0
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 1
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, (temp & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 2
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, (temp & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13, (temp & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 5

        temp_old = temp;
    }
}

void led_task(void)
{
    static uint32_t led1_blink_time = 0; // LED1��˸ʱ���
    static uint8_t led1_blink_state = 0; // LED1��˸״̬

    // LED1����״ָ̬ʾ�߼�
    if (sampling_get_state() == SAMPLING_ACTIVE)
    {
        // 采样状态下使用配置的闪烁周期
        uint32_t current_time = HAL_GetTick();
        uint32_t blink_period = app_config_get_led_blink_period(); // 使用统一配置
        if (current_time - led1_blink_time >= blink_period)
        {
            led1_blink_state ^= 1; // 翻转LED状态
            led1_blink_time = current_time;
        }
        app_state_set_led(0, led1_blink_state); // 使用统一状态管理
    }
    else
    {
        // ֹͣ״̬������
        app_state_set_led(0, 0); // 使用统一状态管理
        led1_blink_state = 0;
    }

    // LED2����״ָ̬ʾ�߼�
    if (sampling_check_overlimit())
    {
        // ����ʱ������LED2
        app_state_set_led(1, 1); // 使用统一状态管理
    }
    else
    {
        // ����ʱ��Ϩ��LED2
        app_state_set_led(1, 0); // 使用统一状态管理
    }

    // 更新LED显示
    uint8_t* ucLed = app_state_get_led_array(); // 获取LED状态数组
    if (ucLed) {
        led_disp(ucLed);
    }
}
