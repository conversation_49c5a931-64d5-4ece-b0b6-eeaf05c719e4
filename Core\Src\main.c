
#include "main.h"
#include "adc.h"
#include "dac.h"
#include "dma.h"
#include "fatfs.h"
#include "i2c.h"
#include "rtc.h"
#include "sdio.h"
#include "spi.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "stdio.h"
#include "mydefine.h"
#include "string.h"
#include "ff.h"
#include "sampling_control.h"
#include "data_storage.h"
#include "device_id.h"
#include "config_manager.h"    // 配置管理系统
#include "app_config.h"        // 统一配置管理
#include "storage_event_handler.h" // 存储事件处理器
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */



u8g2_t u8g2; 
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */
  
  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */
 
  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_ADC1_Init();
  MX_DAC_Init();
  MX_TIM3_Init();
  MX_TIM6_Init();
  MX_TIM14_Init();
  MX_I2C1_Init();
  MX_SPI2_Init();
  MX_SDIO_SD_Init();
  MX_FATFS_Init();
  MX_RTC_Init();
  /* USER CODE BEGIN 2 */
  rt_ringbuffer_init(&uart_ringbuffer, ringbuffer_pool, sizeof(ringbuffer_pool));
  app_btn_init();
  OLED_Init();
  adc_tim_dma_init();
  u8g2_Setup_ssd1306_i2c_128x32_univision_f(&u8g2, U8G2_R0, u8x8_byte_hw_i2c, u8g2_gpio_and_delay_stm32);
  u8g2_InitDisplay(&u8g2);
  u8g2_SetPowerSave(&u8g2, 0);
  spi_flash_init();
  device_id_init();

  // 初始化硬件抽象层
  hal_status_t hal_status = hal_init_all();
  if (hal_status != HAL_STATUS_OK) {
    my_printf(&huart1, "Warning: Hardware abstraction init failed: %s\r\n",
              hal_get_status_string(hal_status));
  }

  // 初始化公共工具模块
  app_config_status_t utils_status = app_utils_init();
  if (utils_status != APP_CONFIG_OK) {
    my_printf(&huart1, "Warning: App utils init failed: %s\r\n",
              app_config_get_status_string(utils_status));
  }

  // 初始化统一配置管理系统
  app_config_status_t config_status = app_config_init();
  if (config_status != APP_CONFIG_OK) {
    my_printf(&huart1, "Warning: App config init failed: %s\r\n",
              app_config_get_status_string(config_status));
  }

  // 初始化统一状态管理系统
  app_config_status_t state_status = app_state_init();
  if (state_status != APP_CONFIG_OK) {
    my_printf(&huart1, "Warning: App state init failed: %s\r\n",
              app_config_get_status_string(state_status));
  }

  // 初始化事件管理系统
  app_config_status_t event_status = app_events_init();
  if (event_status != APP_CONFIG_OK) {
    my_printf(&huart1, "Warning: App events init failed: %s\r\n",
              app_config_get_status_string(event_status));
  }

  // 初始化存储事件处理器
  app_config_status_t storage_handler_status = storage_event_handler_init();
  if (storage_handler_status != APP_CONFIG_OK) {
    my_printf(&huart1, "Warning: Storage event handler init failed: %s\r\n",
              app_config_get_status_string(storage_handler_status));
  }

  config_init();       // 初始化配置系统
  sampling_init();
  data_storage_init();
  scheduler_init();

  // ��ӡ������־��Ϣ
  device_id_print_startup_info();

  data_storage_write_log("system init");

  // ��ӡϵͳ��ʼ�����ɹ���Ϣ
  my_printf(&huart1, "=== System Initialization Complete ===\r\n");
  my_printf(&huart1, "Available commands:\r\n");
  my_printf(&huart1, "  test - System hardware test\r\n");
  my_printf(&huart1, "  RTC Config - Set RTC time\r\n");
  my_printf(&huart1, "  RTC now - Show current time\r\n");
  my_printf(&huart1, "  conf - Read config.ini\r\n");
  my_printf(&huart1, "  ratio - Set ratio parameter\r\n");
  my_printf(&huart1, "  limit - Set limit parameter\r\n");
  my_printf(&huart1, "  config save/read - Save/read parameters\r\n");
  my_printf(&huart1, "  start/stop - Control sampling\r\n");
  my_printf(&huart1, "  hide/unhide - Toggle data format\r\n");
  my_printf(&huart1, "=======================================\r\n");

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */

  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    scheduler_run();

    // 处理事件队列
    app_events_process();
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE3);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 15;
  RCC_OscInitStruct.PLL.PLLN = 144;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 5;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_3) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */

  __disable_irq(); 
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
 
}
#endif /* USE_FULL_ASSERT */
