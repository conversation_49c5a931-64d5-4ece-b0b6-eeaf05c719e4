// 文件名：app_config.h
// 功能：统一应用配置管理头文件，集中管理所有应用层配置参数
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __APP_CONFIG_H__
#define __APP_CONFIG_H__

#include "stdint.h"

// ========== 系统配置常量 ==========
#define APP_CONFIG_VERSION          0x01        // 配置版本号
#define APP_CONFIG_MAGIC            0x41434647  // 配置魔数标识('ACFG')

// ========== 硬件配置参数 ==========
// ADC配置
#define APP_ADC_BUFFER_SIZE         2048        // ADC缓冲区大小
#define APP_ADC_REFERENCE_VOLTAGE   3.3f        // ADC参考电压(V)
#define APP_ADC_RESOLUTION_BITS     12          // ADC分辨率(位)
#define APP_ADC_MAX_VALUE           4096        // ADC最大值(2^12)

// LED配置
#define APP_LED_COUNT               6           // LED数量
#define APP_LED_BLINK_PERIOD_MS     1000        // LED闪烁周期(ms)

// 按键配置
#define APP_BTN_COUNT               6           // 按键数量
#define APP_BTN_DEBOUNCE_MS         20          // 按键消抖时间(ms)
#define APP_BTN_LONG_PRESS_MS       1000        // 长按时间(ms)

// 串口配置
#define APP_UART_BUFFER_SIZE        128         // 串口缓冲区大小
#define APP_UART_BAUDRATE           460800      // 串口波特率
#define APP_UART_TIMEOUT_MS         1000        // 串口超时时间(ms)

// OLED配置
#define APP_OLED_WIDTH              128         // OLED宽度(像素)
#define APP_OLED_HEIGHT             32          // OLED高度(像素)
#define APP_OLED_REFRESH_MS         1           // OLED刷新间隔(ms)

// Flash配置
#define APP_FLASH_CONFIG_ADDR       0x1F0000    // Flash配置存储地址
#define APP_FLASH_PAGE_SIZE         256         // Flash页大小
#define APP_FLASH_SECTOR_SIZE       4096        // Flash扇区大小

// ========== 采样控制配置 ==========
#define APP_SAMPLING_MIN_CYCLE_S    5           // 最小采样周期(秒)
#define APP_SAMPLING_MAX_CYCLE_S    15          // 最大采样周期(秒)
#define APP_SAMPLING_DEFAULT_CYCLE  5           // 默认采样周期(秒)

// ========== 数据存储配置 ==========
#define APP_STORAGE_MAX_RECORDS     10          // 每文件最大记录数
#define APP_STORAGE_FILENAME_LEN    64          // 文件名最大长度
#define APP_STORAGE_PATH_LEN        96          // 路径最大长度
#define APP_STORAGE_BUFFER_SIZE     256         // 存储缓冲区大小

// ========== 任务调度配置 ==========
#define APP_SCHEDULER_MAX_TASKS     10          // 最大任务数量
#define APP_SCHEDULER_MIN_PERIOD    1           // 最小任务周期(ms)

// ========== 参数范围配置 ==========
// 比率参数范围
#define APP_RATIO_MIN               0.0f        // 最小比率值
#define APP_RATIO_MAX               100.0f      // 最大比率值
#define APP_RATIO_DEFAULT           1.0f        // 默认比率值

// 限值参数范围
#define APP_LIMIT_MIN               0.0f        // 最小限值
#define APP_LIMIT_MAX               200.0f      // 最大限值
#define APP_LIMIT_DEFAULT           100.0f      // 默认限值

// 电压参数范围
#define APP_VOLTAGE_MIN             0.0f        // 最小电压值(V)
#define APP_VOLTAGE_MAX             5.0f        // 最大电压值(V)

// ========== 系统状态配置 ==========
#define APP_SYSTEM_BOOT_MAGIC       0x424F4F54  // 启动魔数('BOOT')
#define APP_SYSTEM_MAX_BOOT_COUNT   0xFFFFFFFF  // 最大启动次数

// ========== 调试配置 ==========
#ifdef DEBUG
#define APP_DEBUG_ENABLED           1           // 调试模式使能
#define APP_LOG_LEVEL               3           // 日志级别(0-3)
#else
#define APP_DEBUG_ENABLED           0           // 调试模式禁用
#define APP_LOG_LEVEL               1           // 发布版本日志级别
#endif

// ========== 配置验证宏 ==========
#define APP_CONFIG_VALIDATE_RANGE(val, min, max) \
    ((val) >= (min) && (val) <= (max))

#define APP_CONFIG_VALIDATE_BUFFER_SIZE(size) \
    ((size) > 0 && (size) <= 4096)

#define APP_CONFIG_VALIDATE_TIMEOUT(timeout) \
    ((timeout) > 0 && (timeout) <= 10000)

// ========== 配置状态枚举 ==========
typedef enum {
    APP_CONFIG_OK = 0,              // 配置正常
    APP_CONFIG_ERROR = 1,           // 配置错误
    APP_CONFIG_INVALID_PARAM = 2,   // 参数无效
    APP_CONFIG_OUT_OF_RANGE = 3,    // 参数超出范围
    APP_CONFIG_NOT_INITIALIZED = 4  // 未初始化
} app_config_status_t;

// ========== 配置类型枚举 ==========
typedef enum {
    APP_CONFIG_TYPE_SYSTEM = 0,     // 系统配置
    APP_CONFIG_TYPE_HARDWARE = 1,   // 硬件配置
    APP_CONFIG_TYPE_SAMPLING = 2,   // 采样配置
    APP_CONFIG_TYPE_STORAGE = 3,    // 存储配置
    APP_CONFIG_TYPE_COUNT = 4       // 配置类型数量
} app_config_type_t;

// ========== 配置优先级枚举 ==========
typedef enum {
    APP_CONFIG_PRIORITY_LOW = 0,    // 低优先级
    APP_CONFIG_PRIORITY_NORMAL = 1, // 普通优先级
    APP_CONFIG_PRIORITY_HIGH = 2,   // 高优先级
    APP_CONFIG_PRIORITY_CRITICAL = 3 // 关键优先级
} app_config_priority_t;

// ========== 配置管理接口声明 ==========
app_config_status_t app_config_init(void);                                    // 初始化配置系统
app_config_status_t app_config_validate_all(void);                           // 验证所有配置
app_config_status_t app_config_reset_to_default(app_config_type_t type);     // 重置配置为默认值
app_config_status_t app_config_get_version(uint8_t *version);                // 获取配置版本
const char* app_config_get_status_string(app_config_status_t status);        // 获取状态字符串

// ========== 配置查询接口声明 ==========
uint8_t app_config_is_initialized(void);                                     // 检查是否已初始化
uint32_t app_config_get_adc_buffer_size(void);                              // 获取ADC缓冲区大小
float app_config_get_adc_reference_voltage(void);                           // 获取ADC参考电压
uint32_t app_config_get_led_blink_period(void);                             // 获取LED闪烁周期
uint32_t app_config_get_uart_buffer_size(void);                             // 获取串口缓冲区大小
uint32_t app_config_get_sampling_default_cycle(void);                       // 获取默认采样周期
uint32_t app_config_get_storage_max_records(void);                          // 获取存储最大记录数
float app_config_get_ratio_default(void);                                   // 获取默认比率值
float app_config_get_limit_default(void);                                   // 获取默认限值

// ========== 配置验证接口声明 ==========
uint8_t app_config_validate_ratio(float ratio);                             // 验证比率参数
uint8_t app_config_validate_limit(float limit);                             // 验证限值参数
uint8_t app_config_validate_voltage(float voltage);                         // 验证电压参数
uint8_t app_config_validate_sampling_cycle(uint32_t cycle);                 // 验证采样周期

#endif // __APP_CONFIG_H__
