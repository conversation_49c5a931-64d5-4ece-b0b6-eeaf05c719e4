// 文件名：app_events.h
// 功能：统一事件管理头文件，提供低耦合的模块间通信机制
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __APP_EVENTS_H__
#define __APP_EVENTS_H__

#include "stdint.h"
#include "app_config.h"

// ========== 事件类型枚举 ==========
typedef enum {
    APP_EVENT_NONE = 0,                 // 无事件
    APP_EVENT_SYSTEM_INIT = 1,          // 系统初始化事件
    APP_EVENT_SYSTEM_ERROR = 2,         // 系统错误事件
    APP_EVENT_ADC_CONVERSION_COMPLETE = 3, // ADC转换完成事件
    APP_EVENT_SAMPLING_START = 4,       // 采样开始事件
    APP_EVENT_SAMPLING_STOP = 5,        // 采样停止事件
    APP_EVENT_SAMPLING_DATA_READY = 6,  // 采样数据就绪事件
    APP_EVENT_OVERLIMIT_DETECTED = 7,   // 超限检测事件
    APP_EVENT_UART_DATA_RECEIVED = 8,   // 串口数据接收事件
    APP_EVENT_UART_COMMAND_PARSED = 9,  // 串口命令解析事件
    APP_EVENT_CONFIG_CHANGED = 10,      // 配置变更事件
    APP_EVENT_STORAGE_WRITE_COMPLETE = 11, // 存储写入完成事件
    APP_EVENT_STORAGE_ERROR = 12,       // 存储错误事件
    APP_EVENT_LED_BLINK_UPDATE = 13,    // LED闪烁更新事件
    APP_EVENT_BUTTON_PRESSED = 14,      // 按键按下事件
    APP_EVENT_RTC_TIME_UPDATE = 15,     // RTC时间更新事件
    APP_EVENT_COUNT = 16                // 事件类型总数
} app_event_type_t;

// ========== 事件优先级枚举 ==========
typedef enum {
    APP_EVENT_PRIORITY_LOW = 0,         // 低优先级
    APP_EVENT_PRIORITY_NORMAL = 1,      // 普通优先级
    APP_EVENT_PRIORITY_HIGH = 2,        // 高优先级
    APP_EVENT_PRIORITY_CRITICAL = 3     // 关键优先级
} app_event_priority_t;

// ========== 事件数据结构 ==========
typedef struct {
    float voltage;                      // 电压值
    uint32_t adc_value;                 // ADC原始值
    uint8_t is_overlimit;               // 超限标志
    uint32_t timestamp;                 // 时间戳
} app_event_adc_data_t;

typedef struct {
    uint8_t* data;                      // 数据指针
    uint16_t length;                    // 数据长度
    uint32_t timestamp;                 // 时间戳
} app_event_uart_data_t;

typedef struct {
    uint8_t button_id;                  // 按键ID
    uint8_t press_type;                 // 按键类型(0=短按, 1=长按)
    uint32_t timestamp;                 // 时间戳
} app_event_button_data_t;

typedef struct {
    uint8_t error_code;                 // 错误代码
    const char* error_message;          // 错误消息
    uint32_t timestamp;                 // 时间戳
} app_event_error_data_t;

// ========== 事件联合体 ==========
typedef union {
    app_event_adc_data_t adc_data;      // ADC事件数据
    app_event_uart_data_t uart_data;    // 串口事件数据
    app_event_button_data_t button_data; // 按键事件数据
    app_event_error_data_t error_data;  // 错误事件数据
    uint32_t generic_data;              // 通用数据
} app_event_data_t;

// ========== 事件结构体 ==========
typedef struct {
    app_event_type_t type;              // 事件类型
    app_event_priority_t priority;      // 事件优先级
    app_event_data_t data;              // 事件数据
    uint32_t timestamp;                 // 事件时间戳
    uint8_t processed;                  // 处理标志
} app_event_t;

// ========== 事件回调函数类型 ==========
typedef app_config_status_t (*app_event_callback_t)(const app_event_t* event);

// ========== 事件订阅结构 ==========
typedef struct {
    app_event_type_t event_type;        // 订阅的事件类型
    app_event_callback_t callback;      // 回调函数
    uint8_t enabled;                    // 订阅使能标志
} app_event_subscription_t;

// ========== 事件管理器配置 ==========
#define APP_EVENT_QUEUE_SIZE        16  // 事件队列大小
#define APP_EVENT_MAX_SUBSCRIBERS   32  // 最大订阅者数量

// ========== 事件管理接口声明 ==========
// 初始化和管理
app_config_status_t app_events_init(void);                                    // 初始化事件系统
app_config_status_t app_events_reset(void);                                   // 重置事件系统
uint8_t app_events_is_initialized(void);                                      // 检查是否已初始化

// 事件发布
app_config_status_t app_events_publish(app_event_type_t type, 
                                       app_event_priority_t priority,
                                       const app_event_data_t* data);         // 发布事件
app_config_status_t app_events_publish_simple(app_event_type_t type);         // 发布简单事件(无数据)

// 事件订阅
app_config_status_t app_events_subscribe(app_event_type_t type, 
                                         app_event_callback_t callback);      // 订阅事件
app_config_status_t app_events_unsubscribe(app_event_type_t type, 
                                           app_event_callback_t callback);    // 取消订阅
app_config_status_t app_events_enable_subscription(app_event_type_t type, 
                                                   app_event_callback_t callback); // 启用订阅
app_config_status_t app_events_disable_subscription(app_event_type_t type, 
                                                    app_event_callback_t callback); // 禁用订阅

// 事件处理
app_config_status_t app_events_process(void);                                 // 处理事件队列
app_config_status_t app_events_process_priority(app_event_priority_t min_priority); // 处理指定优先级以上的事件
uint16_t app_events_get_queue_count(void);                                    // 获取队列中事件数量
uint16_t app_events_get_subscriber_count(app_event_type_t type);               // 获取指定事件的订阅者数量

// 事件查询
const char* app_events_get_type_string(app_event_type_t type);                 // 获取事件类型字符串
const char* app_events_get_priority_string(app_event_priority_t priority);     // 获取优先级字符串
app_config_status_t app_events_get_statistics(uint32_t* total_published,
                                              uint32_t* total_processed,
                                              uint32_t* total_errors);        // 获取事件统计信息

// ========== 便捷事件发布接口 ==========
// ADC相关事件
app_config_status_t app_events_publish_adc_complete(float voltage, uint32_t adc_value); // 发布ADC转换完成事件
app_config_status_t app_events_publish_overlimit(float voltage);              // 发布超限事件

// 采样相关事件
app_config_status_t app_events_publish_sampling_start(void);                  // 发布采样开始事件
app_config_status_t app_events_publish_sampling_stop(void);                   // 发布采样停止事件
app_config_status_t app_events_publish_sampling_data(float voltage, uint8_t is_overlimit); // 发布采样数据事件

// 串口相关事件
app_config_status_t app_events_publish_uart_data(uint8_t* data, uint16_t length); // 发布串口数据事件
app_config_status_t app_events_publish_uart_command(void);                    // 发布串口命令事件

// 系统相关事件
app_config_status_t app_events_publish_system_error(uint8_t error_code, const char* message); // 发布系统错误事件
app_config_status_t app_events_publish_config_changed(void);                  // 发布配置变更事件

// 存储相关事件
app_config_status_t app_events_publish_storage_complete(void);                // 发布存储完成事件
app_config_status_t app_events_publish_storage_error(uint8_t error_code);     // 发布存储错误事件

// 按键相关事件
app_config_status_t app_events_publish_button_press(uint8_t button_id, uint8_t press_type); // 发布按键事件

#endif // __APP_EVENTS_H__
