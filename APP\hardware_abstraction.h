// 文件名：hardware_abstraction.h
// 功能：硬件抽象层头文件，提供统一的硬件访问接口
// 作者：西峰电子公司
// 版权：Copyright (c) 2024 西峰电子公司. All rights reserved.

#ifndef __HARDWARE_ABSTRACTION_H__
#define __HARDWARE_ABSTRACTION_H__

#include "stdint.h"
#include "app_config.h"

// ========== 硬件抽象层状态枚举 ==========
typedef enum {
    HAL_STATUS_OK = 0,              // 操作成功
    HAL_STATUS_ERROR = 1,           // 操作失败
    HAL_STATUS_BUSY = 2,            // 硬件忙碌
    HAL_STATUS_TIMEOUT = 3,         // 操作超时
    HAL_STATUS_NOT_INITIALIZED = 4, // 未初始化
    HAL_STATUS_INVALID_PARAM = 5    // 参数无效
} hal_status_t;

// ========== GPIO抽象接口 ==========
typedef enum {
    HAL_GPIO_PIN_RESET = 0,         // GPIO引脚低电平
    HAL_GPIO_PIN_SET = 1            // GPIO引脚高电平
} hal_gpio_pin_state_t;

typedef enum {
    HAL_LED_0 = 0,                  // LED 0
    HAL_LED_1 = 1,                  // LED 1
    HAL_LED_2 = 2,                  // LED 2
    HAL_LED_3 = 3,                  // LED 3
    HAL_LED_4 = 4,                  // LED 4
    HAL_LED_5 = 5,                  // LED 5
    HAL_LED_COUNT = 6               // LED总数
} hal_led_index_t;

// GPIO抽象接口函数
hal_status_t hal_gpio_init(void);                                             // 初始化GPIO
hal_status_t hal_gpio_set_led(hal_led_index_t led_index, hal_gpio_pin_state_t state); // 设置LED状态
hal_status_t hal_gpio_toggle_led(hal_led_index_t led_index);                  // 翻转LED状态
hal_gpio_pin_state_t hal_gpio_get_led_state(hal_led_index_t led_index);       // 获取LED状态

// ========== ADC抽象接口 ==========
typedef struct {
    uint32_t raw_value;             // ADC原始值
    float voltage;                  // 转换后的电压值
    uint32_t timestamp;             // 采样时间戳
    uint8_t channel;                // ADC通道
} hal_adc_result_t;

typedef enum {
    HAL_ADC_MODE_SINGLE = 0,        // 单次转换模式
    HAL_ADC_MODE_CONTINUOUS = 1,    // 连续转换模式
    HAL_ADC_MODE_DMA = 2            // DMA模式
} hal_adc_mode_t;

// ADC抽象接口函数
hal_status_t hal_adc_init(hal_adc_mode_t mode);                               // 初始化ADC
hal_status_t hal_adc_start_conversion(void);                                  // 开始ADC转换
hal_status_t hal_adc_get_result(hal_adc_result_t* out_result);                // 获取ADC结果
hal_status_t hal_adc_stop_conversion(void);                                   // 停止ADC转换
uint8_t hal_adc_is_conversion_complete(void);                                 // 检查转换是否完成

// ========== UART抽象接口 ==========
typedef struct {
    uint32_t baudrate;              // 波特率
    uint8_t data_bits;              // 数据位
    uint8_t stop_bits;              // 停止位
    uint8_t parity;                 // 奇偶校验
} hal_uart_config_t;

// UART抽象接口函数
hal_status_t hal_uart_init(const hal_uart_config_t* config);                  // 初始化UART
hal_status_t hal_uart_transmit(const uint8_t* data, uint16_t length, uint32_t timeout); // 发送数据
hal_status_t hal_uart_receive(uint8_t* data, uint16_t length, uint32_t timeout); // 接收数据
hal_status_t hal_uart_transmit_dma(const uint8_t* data, uint16_t length);     // DMA发送
hal_status_t hal_uart_receive_dma(uint8_t* data, uint16_t length);            // DMA接收
uint16_t hal_uart_get_received_count(void);                                   // 获取已接收字节数

// ========== RTC抽象接口 ==========
typedef struct {
    uint8_t hours;                  // 小时 (0-23)
    uint8_t minutes;                // 分钟 (0-59)
    uint8_t seconds;                // 秒 (0-59)
} hal_rtc_time_t;

typedef struct {
    uint8_t year;                   // 年 (0-99, 表示20xx年)
    uint8_t month;                  // 月 (1-12)
    uint8_t day;                    // 日 (1-31)
    uint8_t weekday;                // 星期 (1-7, 1=周一)
} hal_rtc_date_t;

// RTC抽象接口函数
hal_status_t hal_rtc_init(void);                                              // 初始化RTC
hal_status_t hal_rtc_set_time(const hal_rtc_time_t* time);                    // 设置时间
hal_status_t hal_rtc_get_time(hal_rtc_time_t* out_time);                      // 获取时间
hal_status_t hal_rtc_set_date(const hal_rtc_date_t* date);                    // 设置日期
hal_status_t hal_rtc_get_date(hal_rtc_date_t* out_date);                      // 获取日期
uint32_t hal_rtc_get_timestamp(void);                                         // 获取时间戳

// ========== 定时器抽象接口 ==========
typedef enum {
    HAL_TIMER_MODE_ONE_SHOT = 0,    // 单次触发模式
    HAL_TIMER_MODE_PERIODIC = 1     // 周期触发模式
} hal_timer_mode_t;

typedef void (*hal_timer_callback_t)(void);                                   // 定时器回调函数类型

// 定时器抽象接口函数
hal_status_t hal_timer_init(void);                                            // 初始化定时器
hal_status_t hal_timer_start(uint32_t period_ms, hal_timer_mode_t mode, hal_timer_callback_t callback); // 启动定时器
hal_status_t hal_timer_stop(void);                                            // 停止定时器
uint32_t hal_timer_get_tick(void);                                            // 获取系统时钟计数
hal_status_t hal_timer_delay(uint32_t delay_ms);                              // 延时函数

// ========== Flash存储抽象接口 ==========
typedef struct {
    uint32_t sector_size;           // 扇区大小
    uint32_t page_size;             // 页大小
    uint32_t total_size;            // 总容量
    uint32_t available_size;        // 可用容量
} hal_flash_info_t;

// Flash抽象接口函数
hal_status_t hal_flash_init(void);                                            // 初始化Flash
hal_status_t hal_flash_read(uint32_t address, uint8_t* data, uint32_t length); // 读取数据
hal_status_t hal_flash_write(uint32_t address, const uint8_t* data, uint32_t length); // 写入数据
hal_status_t hal_flash_erase_sector(uint32_t sector_address);                 // 擦除扇区
hal_status_t hal_flash_get_info(hal_flash_info_t* out_info);                  // 获取Flash信息
uint8_t hal_flash_is_busy(void);                                              // 检查Flash是否忙碌

// ========== 显示屏抽象接口 ==========
typedef struct {
    uint16_t width;                 // 显示宽度
    uint16_t height;                // 显示高度
    uint8_t color_depth;            // 颜色深度
} hal_display_info_t;

// 显示屏抽象接口函数
hal_status_t hal_display_init(void);                                          // 初始化显示屏
hal_status_t hal_display_clear(void);                                         // 清屏
hal_status_t hal_display_set_pixel(uint16_t x, uint16_t y, uint8_t color);    // 设置像素
hal_status_t hal_display_draw_string(uint16_t x, uint16_t y, const char* text, uint8_t font_size); // 绘制字符串
hal_status_t hal_display_update(void);                                        // 更新显示
hal_status_t hal_display_get_info(hal_display_info_t* out_info);              // 获取显示信息

// ========== 看门狗抽象接口 ==========
// 看门狗抽象接口函数
hal_status_t hal_watchdog_init(uint32_t timeout_ms);                          // 初始化看门狗
hal_status_t hal_watchdog_refresh(void);                                      // 喂狗
hal_status_t hal_watchdog_start(void);                                        // 启动看门狗
hal_status_t hal_watchdog_stop(void);                                         // 停止看门狗

// ========== 系统控制抽象接口 ==========
// 系统控制抽象接口函数
hal_status_t hal_system_init(void);                                           // 初始化系统
hal_status_t hal_system_reset(void);                                          // 系统复位
hal_status_t hal_system_enter_sleep(void);                                    // 进入睡眠模式
hal_status_t hal_system_enter_standby(void);                                  // 进入待机模式
uint32_t hal_system_get_clock_frequency(void);                                // 获取系统时钟频率
const char* hal_system_get_chip_id(void);                                     // 获取芯片ID

// ========== 硬件抽象层管理接口 ==========
hal_status_t hal_init_all(void);                                              // 初始化所有硬件
hal_status_t hal_deinit_all(void);                                            // 反初始化所有硬件
const char* hal_get_status_string(hal_status_t status);                       // 获取状态字符串
hal_status_t hal_self_test(void);                                             // 硬件自检

#endif // __HARDWARE_ABSTRACTION_H__
